(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5009],{61442:e=>{e.exports={button:"button-PYEOTd6i",disabled:"disabled-PYEOTd6i",hidden:"hidden-PYEOTd6i",icon:"icon-PYEOTd6i",dropped:"dropped-PYEOTd6i"}},88276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},73405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},25884:e=>{e.exports={container:"container-AhaeiE0y",list:"list-AhaeiE0y",overlayScrollWrap:"overlayScrollWrap-AhaeiE0y",scroll:"scroll-AhaeiE0y"}},76521:e=>{e.exports={container:"container-huGG8x61",title:"title-huGG8x61"}},8255:e=>{e.exports={removeButton:"removeButton-BadjY5sX",favoriteButton:"favoriteButton-BadjY5sX",itemRow:"itemRow-BadjY5sX",focused:"focused-BadjY5sX",active:"active-BadjY5sX",actionButton:"actionButton-BadjY5sX",mobile:"mobile-BadjY5sX",itemInfo:"itemInfo-BadjY5sX",title:"title-BadjY5sX",details:"details-BadjY5sX",itemInfoWithPadding:"itemInfoWithPadding-BadjY5sX",favorite:"favorite-BadjY5sX",showOnFocus:"showOnFocus-BadjY5sX"}},18561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},51810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},53790:e=>{e.exports={container:"container-RZoAcQrm",labelWrap:"labelWrap-RZoAcQrm",icon:"icon-RZoAcQrm",text:"text-RZoAcQrm"}},81826:e=>{e.exports={sortButton:"sortButton-mMR_mxxG",
icon:"icon-mMR_mxxG",buttonContainer:"buttonContainer-mMR_mxxG",skeleton:"skeleton-mMR_mxxG"}},99505:e=>{e.exports={button:"button-tFul0OhX","button-children":"button-children-tFul0OhX",hiddenArrow:"hiddenArrow-tFul0OhX",invisibleFocusHandler:"invisibleFocusHandler-tFul0OhX"}},67029:(e,d,t)=>{"use strict";t.d(d,{ControlSkeleton:()=>v,InputClasses:()=>g});var u=t(50959),n=t(97754),o=t(50151),r=t(38528),i=t(90186),a=t(86332),c=t(95604);var s=t(88276),l=t.n(s);function f(e){let d="";return 0!==e&&(1&e&&(d=n(d,l()["no-corner-top-left"])),2&e&&(d=n(d,l()["no-corner-top-right"])),4&e&&(d=n(d,l()["no-corner-bottom-right"])),8&e&&(d=n(d,l()["no-corner-bottom-left"]))),d}function m(e,d,t,u){const{removeRoundBorder:o,className:r,intent:i="default",borderStyle:a="thin",size:s,highlight:m,disabled:h,readonly:g,stretch:b,noReadonlyStyles:p,isFocused:v}=e,_=f(o??(0,c.getGroupCellRemoveRoundBorders)(t));return n(l().container,l()[`container-${s}`],l()[`intent-${i}`],l()[`border-${a}`],s&&l()[`size-${s}`],_,m&&l()["with-highlight"],h&&l().disabled,g&&!p&&l().readonly,v&&l().focused,b&&l().stretch,d&&l().grouped,!u&&l()["adjust-position"],t.isTop&&l()["first-row"],t.isLeft&&l()["first-col"],r)}function h(e,d,t){const{highlight:u,highlightRemoveRoundBorder:o}=e;if(!u)return l().highlight;const r=f(o??(0,c.getGroupCellRemoveRoundBorders)(d));return n(l().highlight,l().shown,l()[`size-${t}`],r)}const g={FontSizeMedium:(0,o.ensureDefined)(l()["font-size-medium"]),FontSizeLarge:(0,o.ensureDefined)(l()["font-size-large"])},b={passive:!1};function p(e,d){const{style:t,id:n,role:o,onFocus:c,onBlur:s,onMouseOver:l,onMouseOut:f,onMouseDown:g,onMouseUp:p,onKeyDown:v,onClick:_,tabIndex:y,startSlot:w,middleSlot:x,endSlot:N,onWheel:C,onWheelNoPassive:S=null,size:E,tag:D="span",type:R}=e,{isGrouped:I,cellState:k,disablePositionAdjustment:P=!1}=(0,u.useContext)(a.ControlGroupContext),F=function(e,d=null,t){const n=(0,u.useRef)(null),o=(0,u.useRef)(null),r=(0,u.useCallback)((()=>{if(null===n.current||null===o.current)return;const[e,d,t]=o.current;null!==d&&n.current.addEventListener(e,d,t)}),[]),i=(0,u.useCallback)((()=>{if(null===n.current||null===o.current)return;const[e,d,t]=o.current;null!==d&&n.current.removeEventListener(e,d,t)}),[]),a=(0,u.useCallback)((e=>{i(),n.current=e,r()}),[]);return(0,u.useEffect)((()=>(o.current=[e,d,t],r(),i)),[e,d,t]),a}("wheel",S,b),z=D;return u.createElement(z,{type:R,style:t,id:n,role:o,className:m(e,I,k,P),tabIndex:y,ref:(0,r.useMergedRefs)([d,F]),onFocus:c,onBlur:s,onMouseOver:l,onMouseOut:f,onMouseDown:g,onMouseUp:p,onKeyDown:v,onClick:_,onWheel:C,...(0,i.filterDataProps)(e),...(0,i.filterAriaProps)(e)},w,x,N,u.createElement("span",{className:h(e,k,E)}))}p.displayName="ControlSkeleton";const v=u.forwardRef(p)},78274:(e,d,t)=>{"use strict";t.d(d,{AfterSlot:()=>s,EndSlot:()=>c,MiddleSlot:()=>a,StartSlot:()=>i});var u=t(50959),n=t(97754),o=t(73405),r=t.n(o);function i(e){const{className:d,interactive:t=!0,icon:o=!1,children:i}=e;return u.createElement("span",{
className:n(r()["inner-slot"],t&&r().interactive,o&&r().icon,d)},i)}function a(e){const{className:d,children:t}=e;return u.createElement("span",{className:n(r()["inner-slot"],r()["inner-middle-slot"],d)},t)}function c(e){const{className:d,interactive:t=!0,icon:o=!1,children:i}=e;return u.createElement("span",{className:n(r()["inner-slot"],t&&r().interactive,o&&r().icon,d)},i)}function s(e){const{className:d,children:t}=e;return u.createElement("span",{className:n(r()["after-slot"],d)},t)}},66686:(e,d,t)=>{"use strict";t.d(d,{useComposedKeyboardActionHandlers:()=>i,useKeyboardActionHandler:()=>r,useKeyboardClose:()=>s,useKeyboardEventHandler:()=>a,useKeyboardOpen:()=>l,useKeyboardToggle:()=>c});var u=t(50959),n=t(3343);const o=()=>!0;function r(e,d,t=o,n){return(0,u.useCallback)((u=>{if(n){if("horizontal"===n&&(40===u||38===u))return;if("vertical"===n&&(37===u||39===u))return}const o=e.map((e=>"function"==typeof e?e():e));return!(!t(u)||!o.includes(u))&&(d(u),!0)}),[...e,d,t])}function i(...e){return(0,u.useCallback)((d=>{for(const t of e)if(t(d))return!0;return!1}),[...e])}function a(e,d=!0,t=!1){const o=i(...e);return(0,u.useCallback)((e=>{const u=o((0,n.hashFromEvent)(e));u&&d&&e.preventDefault(),u&&t&&e.stopPropagation()}),[o])}function c(e,d=!0){return r([13,32],e,(function(e){if(13===e)return d;return!0}))}function s(e,d){return r([9,(0,u.useCallback)((()=>n.Modifiers.Shift+9),[]),27],d,(0,u.useCallback)((()=>e),[e]))}function l(e,d){return r([40,38],d,(0,u.useCallback)((()=>!e),[e]))}},36104:(e,d,t)=>{"use strict";t.d(d,{useControlDisclosure:()=>n});var u=t(7953);function n(e){const{intent:d,highlight:t,...n}=e,{isFocused:o,...r}=(0,u.useDisclosure)(n);return{...r,isFocused:o,highlight:t??o,intent:d??(o?"primary":"default")}}},7953:(e,d,t)=>{"use strict";t.d(d,{useDisclosure:()=>c});var u=t(50959),n=t(50151),o=t(54717),r=t(29202),i=t(47201),a=t(22064);function c(e){const{id:d,listboxId:t,disabled:c,buttonTabIndex:s=0,onFocus:l,onBlur:f,onClick:m}=e,[h,g]=(0,u.useState)(!1),[b,p]=(0,r.useFocus)(),v=b||h,_=t??void 0!==d?(0,a.createDomId)(d,"listbox"):void 0,y=(0,u.useRef)(null),w=(0,u.useCallback)((e=>y.current?.focus(e)),[y]),x=(0,u.useRef)(null),N=(0,u.useCallback)((()=>(0,n.ensureNotNull)(x.current).focus()),[x]),C=(0,u.useCallback)((()=>g(!0)),[g]),S=(0,u.useCallback)(((e=!1,d=!1)=>{g(!1);const{activeElement:t}=document;t&&(0,o.isTextEditingField)(t)||d||w({preventScroll:e})}),[g,w]),E=(0,u.useCallback)((()=>{h?S():C()}),[h,S,C]),D=c?[]:[l,p.onFocus],R=c?[]:[f,p.onBlur],I=c?[]:[m,E],k=(0,i.createSafeMulticastEventHandler)(...D),P=(0,i.createSafeMulticastEventHandler)(...R),F=(0,i.createSafeMulticastEventHandler)(...I);return{listboxId:_,isOpened:h,isFocused:v,buttonTabIndex:c?-1:s,listboxTabIndex:-1,open:C,close:S,toggle:E,onOpen:N,buttonFocusBindings:{onFocus:k,onBlur:P},onButtonClick:F,buttonRef:y,listboxRef:x,buttonAria:{"aria-controls":h?_:void 0,"aria-expanded":h,"aria-disabled":c}}}},29202:(e,d,t)=>{"use strict";t.d(d,{useFocus:()=>n});var u=t(50959);function n(e,d){const[t,n]=(0,u.useState)(!1);(0,
u.useEffect)((()=>{d&&t&&n(!1)}),[d,t]);const o={onFocus:(0,u.useCallback)((function(d){void 0!==e&&e.current!==d.target||n(!0)}),[e]),onBlur:(0,u.useCallback)((function(d){void 0!==e&&e.current!==d.target||n(!1)}),[e])};return[t,o]}},38528:(e,d,t)=>{"use strict";t.d(d,{useMergedRefs:()=>o});var u=t(50959),n=t(53017);function o(e){return(0,u.useCallback)((0,n.mergeRefs)(e),e)}},67842:(e,d,t)=>{"use strict";t.d(d,{useResizeObserver:()=>r});var u=t(50959),n=t(43010),o=t(39416);function r(e,d=[]){const{callback:t,ref:r=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),i=(0,u.useRef)(null),a=(0,u.useRef)(t);a.current=t;const c=(0,o.useFunctionalRefObject)(r),s=(0,u.useCallback)((e=>{c(e),null!==i.current&&(i.current.disconnect(),null!==e&&i.current.observe(e))}),[c,i]);return(0,n.useIsomorphicLayoutEffect)((()=>(i.current=new ResizeObserver(((e,d)=>{a.current(e,d)})),c.current&&s(c.current),()=>{i.current?.disconnect()})),[c,...d]),s}},47930:(e,d,t)=>{"use strict";t.d(d,{formatTime:()=>f,isValidTimeOptionsDateStyle:()=>l,isValidTimeOptionsRange:()=>s});const u={calendar:"gregory",numberingSystem:"latn",hour12:!1},n={year:"numeric",month:"short",day:"numeric"},o={year:"numeric",month:"2-digit",day:"2-digit"},r={hour:"2-digit",minute:"2-digit",second:"2-digit"},i={timeZoneName:"shortOffset",weekday:"short"},a={year:0,month:1,day:2,hour:3,minute:4,second:5};const c=["year","month","day","hour","minute","second"];function s(e){return c.includes(e)}function l(e){return"numeric"===e||"short"===e}function f(e,d,t="year",c="day",s){const l=function(e="year",d="day",t={}){[e,d]=a[d]>a[e]?[e,d]:[d,e];const c={..."numeric"===t.dateStyle?o:n,...r},s=t.fractionalSecondDigits,l={...u,fractionalSecondDigits:void 0===s?void 0:Math.floor(Math.min(Math.max(1,s),3)),timeZone:t.timeZone,weekday:t.weekday?i.weekday:void 0,timeZoneName:t.timeZoneName?i.timeZoneName:void 0};return Object.keys(c).forEach((t=>{a[t]>=a[e]&&a[t]<=a[d]&&(l[t]=c[t])})),l}(t,c,s),f=new Intl.DateTimeFormat(d,l),m=new Date(e);return f.format(m)}},22064:(e,d,t)=>{"use strict";t.d(d,{createDomId:()=>f,joinDomIds:()=>m});const u="id",n=/\s/g,o="-",r="_",i=" ";function a(e){return"string"==typeof e}function c(e){switch(typeof e){case"string":return e;case"number":case"bigint":return e.toString(10);case"boolean":case"symbol":return e.toString();default:return null}}function s(e){return e.trim().length>0}function l(e){return e.replace(n,o)}function f(...e){const d=e.map(c).filter(a).filter(s).map(l);return(d.length>0&&d[0].startsWith(u+r)?d:[u,...d]).join(r)}function m(...e){return e.map(c).filter(a).filter(s).join(i)}},58653:(e,d,t)=>{"use strict";t.d(d,{getLocaleIso:()=>r});var u=t(50151)
;const n=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true,"disabled":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),o=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return n;const d={};return e.forEach((e=>{const t=(0,u.ensureNotNull)(e.getAttribute("data-locale"));d[t]={...n[t],href:e.href}})),d}();function r(e){return e=e||window.locale,o[e]?.iso}},64530:(e,d,t)=>{"use strict";t.d(d,{DialogContentItem:()=>f});var u=t(50959),n=t(97754),o=t.n(n),r=t(49483),i=t(36189),a=t(96040);function c(e){const{url:d,...t}=e;return d?u.createElement("a",{...t,href:d}):u.createElement("div",{...t})}var s=t(60925),l=t(8255);function f(e){const{title:d,subtitle:t,removeBtnLabel:n,onClick:f,onClickFavorite:h,onClickRemove:g,isActive:b,isFavorite:p,isFocused:v,isMobile:_=!1,showFavorite:y=!0,focusedActionIndex:w,className:x,tabIndex:N,index:C,focusVisible:S,getElementId:E,...D}=e,R=[y&&h?"favorite":null,"remove"].filter((e=>null!==e)),I={favorite:R.indexOf("favorite"),remove:R.indexOf("remove")},k=(0,u.useId)();return u.createElement(c,{...D,role:"row",id:E?.(C),className:o()(l.itemRow,b&&l.active,_&&l.mobile,v&&S&&null===w&&l.focused,x),tabIndex:N,onClick:m.bind(null,f),"data-role":"list-item","data-active":b,"aria-labelledby":k},y&&h&&u.createElement(i.FavoriteButton,{id:E?.(C,I.favorite),role:"cell",className:o()(l.favoriteButton,l.actionButton,p&&l.favorite,r.CheckMobile.any()&&l.mobile,v&&w===I.favorite&&l.focused,v&&S&&l.showOnFocus),isActive:b,isFilled:p,onClick:m.bind(null,h),"data-name":"list-item-favorite-button","data-role":"list-item-action","data-favorite":p}),u.createElement("div",{id:k,role:"cell",className:o()(l.itemInfo,!y&&l.itemInfoWithPadding)},u.createElement("div",{className:o()(l.title,b&&l.active,_&&l.mobile),"data-name":"list-item-title"},d),u.createElement("div",{className:o()(l.details,b&&l.active,_&&l.mobile)},t)),u.createElement(a.RemoveButton,{id:E?.(C,I.remove),role:"cell",className:o()(l.removeButton,l.actionButton,v&&w===I.remove&&l.focused,v&&S&&l.showOnFocus),isActive:b,onClick:m.bind(null,g),"data-name":"list-item-remove-button","data-role":"list-item-action",title:n,icon:s}))}function m(e,d){d.defaultPrevented||(d.preventDefault(),e(d))}},3085:(e,d,t)=>{"use strict";t.d(d,{OverlayScrollContainer:()=>p});var u=t(50959),n=t(97754),o=t.n(n),r=t(63273),i=t(50151),a=t(9859);const c=t(51810);var s;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(s||(s={}));const l={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},f=40;function m(e){
const{size:d,scrollSize:t,clientSize:n,scrollProgress:r,onScrollProgressChange:s,scrollMode:m,theme:h=c,onDragStart:g,onDragEnd:b,minBarSize:p=f}=e,v=(0,u.useRef)(null),_=(0,u.useRef)(null),[y,w]=(0,u.useState)(!1),x=(0,u.useRef)(0),{isHorizontal:N,isNegative:C,sizePropName:S,minSizePropName:E,startPointPropName:D,currentMousePointPropName:R,progressBarTransform:I}=l[m];(0,u.useEffect)((()=>{const e=(0,i.ensureNotNull)(v.current).ownerDocument;return y?(g&&g(),e&&(e.addEventListener("mousemove",O),e.addEventListener("mouseup",L))):b&&b(),()=>{e&&(e.removeEventListener("mousemove",O),e.removeEventListener("mouseup",L))}}),[y]);const k=d/t||0,P=n*k||0,F=Math.max(P,p),z=(d-F)/(d-P),B=t-d,T=C?-B:0,W=C?0:B,M=Z((0,a.clamp)(r,T,W))||0;return u.createElement("div",{ref:v,className:o()(h.wrap,N&&h["wrap--horizontal"]),style:{[S]:d},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const d=A(e.nativeEvent,(0,i.ensureNotNull)(v.current)),t=Math.sign(d),u=(0,i.ensureNotNull)(_.current).getBoundingClientRect();x.current=t*u[S]/2;let n=Math.abs(d)-Math.abs(x.current);const o=Z(B);n<0?(n=0,x.current=d):n>o&&(n=o,x.current=d-t*o);s(H(t*n)),w(!0)}},u.createElement("div",{ref:_,className:o()(h.bar,N&&h["bar--horizontal"]),style:{[E]:p,[S]:F,transform:`${I}(${M}px)`},onMouseDown:function(e){e.preventDefault(),x.current=A(e.nativeEvent,(0,i.ensureNotNull)(_.current)),w(!0)}},u.createElement("div",{className:o()(h.barInner,N&&h["barInner--horizontal"])})));function O(e){const d=A(e,(0,i.ensureNotNull)(v.current))-x.current;s(H(d))}function L(){w(!1)}function A(e,d){const t=d.getBoundingClientRect()[D];return e[R]-t}function Z(e){return e*k*z}function H(e){return e/k/z}}var h=t(70412),g=t(18561);const b=8;function p(e){const{reference:d,className:t,containerHeight:o=0,containerWidth:i=0,contentHeight:a=0,contentWidth:c=0,scrollPosTop:s=0,scrollPosLeft:l=0,onVerticalChange:f,onHorizontalChange:p,visible:v}=e,[_,y]=(0,h.useHover)(),[w,x]=(0,u.useState)(!1),N=o<a,C=i<c,S=N&&C?b:0;return u.createElement("div",{...y,ref:d,className:n(t,g.scrollWrap),style:{visibility:v||_||w?"visible":"hidden"}},N&&u.createElement(m,{size:o-S,scrollSize:a-S,clientSize:o-S,scrollProgress:s,onScrollProgressChange:function(e){f&&f(e)},onDragStart:E,onDragEnd:D,scrollMode:0}),C&&u.createElement(m,{size:i-S,scrollSize:c-S,clientSize:i-S,scrollProgress:l,onScrollProgressChange:function(e){p&&p(e)},onDragStart:E,onDragEnd:D,scrollMode:(0,r.isRtl)()?2:1}));function E(){x(!0)}function D(){x(!1)}}},10087:(e,d,t)=>{"use strict";t.d(d,{getElementId:()=>l,useSearchDialogKeyboardNavigation:()=>s});var u=t(50959),n=t(50335),o=t(3343),r=t(76460),i=t(19291),a=t(9859),c=t(49483);function s(e){const{contentContainerRef:d,searchInputRef:t,dialogRef:n,getNextFocusedItemIndex:s,isNavigationDisabled:f,onEscapeClick:m,scrollToFocusedItem:h,getElementIdByIndex:g=l}=e,[b,p]=(0,u.useState)(null),[v,_]=(0,u.useState)(null),y=(0,u.useRef)({itemIndex:null,actionIndex:null}),[w,x]=(0,u.useState)(!1);function N(){x(!1),_(null),p(null)}return(0,
u.useLayoutEffect)((()=>{if(null===b||!w)return;const e=g(b,v),t=d.current?.querySelector(`#${e}`);return t?.dispatchEvent(new CustomEvent("active-descendant-focus")),()=>{t?.dispatchEvent(new CustomEvent("active-descendant-blur"))}}),[b,w,v]),{handleKeyDown:function(e){const u=(0,o.hashFromEvent)(e);if(27===u&&m)return void m(e);const n=d.current;if(!n||e.target!==t.current)return;if(32===u||13===u){if(null===b)return;e.preventDefault();const d=n.querySelector(`#${g(b,v)}`);if(!(d instanceof HTMLElement))return;return void d.click()}const r=(0,i.mapKeyCodeToDirection)(u);if(!r||f)return;const c=null!==b?n.querySelector(`#${g(b)}`):null;switch(r){case"blockNext":case"blockPrev":{e.preventDefault();const d=s(b,"blockNext"===r?1:-1);if(null===d)return;p(d),x(!0),y.current.itemIndex=d;const t=n.querySelector(`#${g(d)}`);if(h(t,d),null!==v&&t instanceof HTMLElement){const e=Array.from(t.querySelectorAll('[data-role="list-item-action"]'));if(!e.length)return _(null),void(y.current.actionIndex=null);const d=(0,a.clamp)(v,0,e.length-1);_(d),y.current.actionIndex=d}return}case"inlineNext":{if(!c)return;e.preventDefault();const d=Array.from(c.querySelectorAll('[data-role="list-item-action"]'));if(!d||!d.length)return;return null===v?(_(0),void(y.current.actionIndex=0)):v===d.length-1?(_(null),void(y.current.actionIndex=null)):(_(v+1),void(y.current.actionIndex=v+1))}case"inlinePrev":{if(!c)return;e.preventDefault();const d=Array.from(c.querySelectorAll('[data-role="list-item-action"]'));if(!d||!d.length)return;return null===v?(_(d.length-1),void(y.current.actionIndex=d.length-1)):0===v?(_(null),void(y.current.actionIndex=null)):(_(v-1),void(y.current.actionIndex=v-1))}}},handleForceFocus:function(){(0,i.updateTabIndexes)()},handleSearchRefBlur:function(e){N(),p(b),c.CheckMobile.any()||e.relatedTarget!==n.current?.getElement()||e.target.focus()},resetFocusState:N,restoreFocusState:function(){_(y.current.actionIndex),p(y.current.itemIndex),x(!0)},focusVisible:w,focusedItemIndex:b,focusedActionIndex:v,setFocusedItemIndex:p,setFocusedActionIndex:_,onDialogClick:function(e){(0,r.isKeyboardClick)(e)||(x(!1),_(null))}}}function l(e,d){return(0,n.isNumber)(d)&&-1!==d?`list-item-${e}-action-${d}`:`list-item-${e}`}},59054:(e,d,t)=>{"use strict";t.d(d,{ControlDisclosureView:()=>b});var u=t(50959),n=t(97754),o=t.n(n),r=t(38528),i=t(67029),a=t(78274),c=t(4523),s=t(9745),l=t(2948),f=t(61442);function m(e){const{isDropped:d}=e;return u.createElement(s.Icon,{className:o()(f.icon,d&&f.dropped),icon:l})}function h(e){const{className:d,disabled:t,isDropped:n}=e;return u.createElement("span",{className:o()(f.button,t&&f.disabled,d)},u.createElement(m,{isDropped:n}))}var g=t(99505);const b=u.forwardRef(((e,d)=>{
const{listboxId:t,className:n,listboxClassName:s,listboxTabIndex:l,hideArrowButton:f,matchButtonAndListboxWidths:m,popupPosition:b,disabled:p,isOpened:v,scrollWrapReference:_,repositionOnScroll:y,closeOnHeaderOverlap:w,listboxReference:x,size:N="small",onClose:C,onOpen:S,onListboxFocus:E,onListboxBlur:D,onListboxKeyDown:R,buttonChildren:I,children:k,caretClassName:P,buttonContainerClassName:F,listboxAria:z,...B}=e,T=(0,u.useRef)(null),W=!f&&u.createElement(a.EndSlot,null,u.createElement(h,{isDropped:v,disabled:p,className:P}));return u.createElement(c.PopupMenuDisclosureView,{buttonRef:T,listboxId:t,listboxClassName:s,listboxTabIndex:l,isOpened:v,onClose:C,onOpen:S,listboxReference:x,scrollWrapReference:_,onListboxFocus:E,onListboxBlur:D,onListboxKeyDown:R,listboxAria:z,matchButtonAndListboxWidths:m,popupPosition:b,button:u.createElement(i.ControlSkeleton,{...B,"data-role":"listbox",disabled:p,className:o()(g.button,n),size:N,ref:(0,r.useMergedRefs)([T,d]),middleSlot:u.createElement(a.MiddleSlot,null,u.createElement("span",{className:o()(g["button-children"],f&&g.hiddenArrow,F)},I)),endSlot:W}),popupChildren:k,repositionOnScroll:y,closeOnHeaderOverlap:w})}));b.displayName="ControlDisclosureView"},95276:(e,d,t)=>{"use strict";t.d(d,{ControlDisclosure:()=>s});var u=t(50959),n=t(38528),o=t(26597),r=t(59054),i=t(36104),a=t(68335),c=t(99505);const s=u.forwardRef(((e,d)=>{const{id:t,tabIndex:s,disabled:l,highlight:f,intent:m,children:h,onClick:g,onFocus:b,onBlur:p,listboxAria:v,onListboxKeyDown:_,...y}=e,w=(0,u.useRef)({"aria-labelledby":t}),{listboxId:x,isOpened:N,isFocused:C,buttonTabIndex:S,listboxTabIndex:E,highlight:D,intent:R,onOpen:I,close:k,toggle:P,buttonFocusBindings:F,onButtonClick:z,buttonRef:B,listboxRef:T,buttonAria:W}=(0,i.useControlDisclosure)({id:t,disabled:l,buttonTabIndex:s,intent:m,highlight:f,onFocus:b,onBlur:p,onClick:g}),M=(0,o.useKeyboardToggle)(P),O=(0,o.useKeyboardClose)(N,k),L=(0,o.useKeyboardEventHandler)([M,O]);return u.createElement(r.ControlDisclosureView,{...y,...F,...W,id:t,role:"button",tabIndex:S,disabled:l,isOpened:N,isFocused:C,ref:(0,n.useMergedRefs)([B,d]),highlight:D,intent:R,onClose:k,onOpen:I,onClick:z,onKeyDown:L,listboxId:x,listboxTabIndex:E,listboxReference:T,listboxAria:v??w.current,onListboxKeyDown:function(e){if(27===(0,a.hashFromEvent)(e))return e.preventDefault(),void k();_?.(e)}},h,u.createElement("span",{className:c.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:()=>k()}))}));s.displayName="ControlDisclosure"},4523:(e,d,t)=>{"use strict";t.d(d,{PopupMenuDisclosureView:()=>s});var u=t(50959),n=t(20520),o=t(50151);const r={x:0,y:0};function i(e,d,t){return(0,u.useCallback)((()=>function(e,d,{x:t=r.x,y:u=r.y}=r){const n=(0,o.ensureNotNull)(e).getBoundingClientRect(),i={x:n.left+t,y:n.top+n.height+u,indentFromWindow:{top:4,bottom:4,left:4,right:4}};return d&&(i.overrideWidth=n.width),i}(e.current,d,t)),[e,d])}var a=t(86240);const c=parseInt(a["size-header-height"]);function s(e){
const{button:d,popupChildren:t,buttonRef:o,listboxId:r,listboxClassName:a,listboxTabIndex:s,matchButtonAndListboxWidths:l,isOpened:f,scrollWrapReference:m,listboxReference:h,onClose:g,onOpen:b,onListboxFocus:p,onListboxBlur:v,onListboxKeyDown:_,listboxAria:y,repositionOnScroll:w=!0,closeOnHeaderOverlap:x=!1,popupPositionCorrection:N={x:0,y:0},popupPosition:C}=e,S=i(o,l,N),E=x?c:0;return u.createElement(u.Fragment,null,d,u.createElement(n.PopupMenu,{...y,id:r,className:a,tabIndex:s,isOpened:f,position:C||S,repositionOnScroll:w,onClose:g,onOpen:b,doNotCloseOn:o.current,reference:h,scrollWrapReference:m,onFocus:p,onBlur:v,onKeyDown:_,closeOnScrollOutsideOffset:E},t))}},26597:(e,d,t)=>{"use strict";t.d(d,{useKeyboardActionHandler:()=>u.useKeyboardActionHandler,useKeyboardClose:()=>u.useKeyboardClose,useKeyboardEventHandler:()=>u.useKeyboardEventHandler,useKeyboardOpen:()=>u.useKeyboardOpen,useKeyboardToggle:()=>u.useKeyboardToggle});var u=t(66686)},898:(e,d,t)=>{"use strict";t.d(d,{useDimensions:()=>o});var u=t(50959),n=t(67842);function o(e){const[d,t]=(0,u.useState)(null),o=(0,u.useCallback)((([e])=>{const u=e.target.getBoundingClientRect();u.width===d?.width&&u.height===d.height||t(u)}),[d]);return[(0,n.useResizeObserver)({callback:o,ref:e}),d]}},78036:(e,d,t)=>{"use strict";t.d(d,{useEnsuredContext:()=>o});var u=t(50959),n=t(50151);function o(e){return(0,n.ensureNotNull)((0,u.useContext)(e))}},33127:(e,d,t)=>{"use strict";t.d(d,{useOverlayScroll:()=>a});var u=t(50959),n=t(50151),o=t(70412),r=t(49483);const i={onMouseOver:()=>{},onMouseOut:()=>{}};function a(e,d=r.CheckMobile.any()){const t=(0,u.useRef)(null),a=e||(0,u.useRef)(null),[c,s]=(0,o.useHover)(),[l,f]=(0,u.useState)({reference:t,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){f((d=>({...d,scrollPosTop:e}))),(0,n.ensureNotNull)(a.current).scrollTop=e},onHorizontalChange:function(e){f((d=>({...d,scrollPosLeft:e}))),(0,n.ensureNotNull)(a.current).scrollLeft=e},visible:c}),m=(0,u.useCallback)((()=>{if(!a.current)return;const{clientHeight:e,scrollHeight:d,scrollTop:u,clientWidth:n,scrollWidth:o,scrollLeft:r}=a.current,i=t.current?t.current.offsetTop:0;f((t=>({...t,containerHeight:e-i,contentHeight:d-i,scrollPosTop:u,containerWidth:n,contentWidth:o,scrollPosLeft:r})))}),[]);function h(){f((e=>({...e,scrollPosTop:(0,n.ensureNotNull)(a.current).scrollTop,scrollPosLeft:(0,n.ensureNotNull)(a.current).scrollLeft})))}return(0,u.useEffect)((()=>{c&&m(),f((e=>({...e,visible:c})))}),[c]),(0,u.useEffect)((()=>{const e=a.current;return e&&e.addEventListener("scroll",h),()=>{e&&e.removeEventListener("scroll",h)}}),[a]),[l,d?i:s,a,m]}},77975:(e,d,t)=>{"use strict";t.d(d,{useWatchedValueReadonly:()=>n});var u=t(50959);const n=(e,d=!1,t=[])=>{const n="watchedValue"in e?e.watchedValue:void 0,o="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[r,i]=(0,u.useState)(n?n.value():o);return(d?u.useLayoutEffect:u.useEffect)((()=>{if(n){i(n.value());const e=e=>i(e);return n.subscribe(e),
()=>n.unsubscribe(e)}return()=>{}}),[n,...t]),r}},14896:e=>{e.exports={highlight:"highlight-6tu1aYjZ",active:"active-6tu1aYjZ"}},8692:e=>{e.exports={dialog:"dialog-T4Q8BJPb",contentList:"contentList-T4Q8BJPb",contentHeader:"contentHeader-T4Q8BJPb",emptyState:"emptyState-T4Q8BJPb"}},55127:(e,d,t)=>{"use strict";t.r(d),t.d(d,{LoadChartDialogRenderer:()=>we});var u,n,o=t(50959),r=t(97754),i=t.n(r),a=t(50151),c=t(11542),s=t(56840),l=t(49483),f=t(77975),m=t(79418),h=t(9745),g=t(95276),b=t(20243),p=t(44563),v=t(645),_=t(81826);function y(e){const{sortDirection:d,children:t,...u}=e,n=(0,o.useRef)(null);return o.createElement("div",{...u,className:r(_.sortButton,"apply-common-tooltip","common-tooltip-vertical")},o.createElement(g.ControlDisclosure,{hideArrowButton:!0,ref:n,buttonChildren:o.createElement(h.Icon,{className:_.icon,icon:0===d?p:v}),buttonContainerClassName:_.buttonContainer,className:_.skeleton,onListboxFocus:function(e){e.target instanceof HTMLElement&&(0,b.handleAccessibleMenuFocus)(e,n)},onListboxKeyDown:b.handleAccessibleMenuKeyDown,"aria-label":e.title,"aria-haspopup":"menu"},t))}!function(e){e.Modified="modified",e.Title="title",e.Expiration="expiration"}(u||(u={})),function(e){e[e.Asc=0]="Asc",e[e.Desc=1]="Desc"}(n||(n={}));var w=t(10838),x=t(53790);function N(e){const{label:d,listSortField:t,itemSortField:u,listSortDirection:n,itemSortDirection:i,onClick:a,className:c,...s}=e,l=u===t&&i===n;return o.createElement(w.AccessibleMenuItem,{...s,role:"menuitemradio",isActive:l,className:r(x.container,c),label:o.createElement("div",{className:x.labelWrap},o.createElement(h.Icon,{className:x.icon,icon:0===i?p:v}),o.createElement("span",{className:x.text},d)),onClick:function(){a(u,i)},"aria-checked":l,"data-active":l.toString(),"data-sort-field":u,"data-sort-direction":0===i?"asc":"desc"})}var C=t(69654),S=t(76521);function E(e){const{children:d,className:t}=e;return o.createElement("div",{className:i()(S.container,t)},d)}function D(e){const{title:d}=e;return o.createElement("div",{className:S.title},d)}var R=t(50335);var I=t(84952),k=t(63273),P=t(898),F=t(33127);var z=t(3085),B=t(25884);function T(e){const{id:d,reference:t,role:u,className:n,onScroll:r,onTouchStart:a,scrollbar:c,contentContainerRef:s,children:f,...m}=e,[h,g]=(0,P.useDimensions)(s),[b,p,v,_]=(0,F.useOverlayScroll)();return(0,o.useEffect)((()=>{const e=()=>{};return l.isFF?(document.addEventListener("wheel",(()=>e)),()=>{document.removeEventListener("wheel",e)}):e}),[]),o.createElement("div",{id:d,role:u,..."overlay"===c&&p,className:i()(B.container,n),onTouchStart:a,onScrollCapture:r,ref:h},"overlay"===c&&o.createElement(z.OverlayScrollContainer,{...b,className:B.overlayScrollWrap}),o.createElement(I.FixedSizeList,{ref:t,className:i()("native"===c?B.scroll:B.list),outerRef:"overlay"===c?v:void 0,onItemsRendered:_,layout:"vertical",width:"100%",height:g?.height||0,children:f,direction:(0,k.isRtl)()?"rtl":"ltr",...m}))}var W=t(84015);var M=t(56570),O=t(64530),L=t(10074),A=t(50655),Z=t(3615);var H=t(24637),j=t(97006),V=t(10087),K=t(78036)
;const Y=o.createContext(null);var X=t(56127),$=t(58653),G=t(47930);function q(){if(window&&!window.customElements.get("time-format")){class e extends HTMLElement{constructor(){super(),this.timestamp=NaN,this.shadow=this.attachShadow({mode:"closed"})}connectedCallback(){this.updateText()}disconnectedCallback(){}attributeChangedCallback(e,d,t){if("timestamp"===e){const e=Q(t);if(!e)return;this.timestamp=e}if("locale"===e&&(this.locale=t||void 0),"from"===e||"to"===e){if(!t)return;if(!(0,G.isValidTimeOptionsRange)(t))return;this[e]=t}"date-style"===e&&(this.dateStyle=t&&(0,G.isValidTimeOptionsDateStyle)(t)?t:void 0),"time-zone-name"===e&&(this.timeZoneName=J(t)),"weekday"===e&&(this.weekday=J(t)),"fractional-second-digits"===e&&(this.fractionalSecondDigits=Q(t)),"time-zone"===e&&(this.timeZone=t||void 0),this.updateText()}updateText(){if(!isNaN(this.timestamp))try{this.shadow.innerHTML=(0,G.formatTime)(this.timestamp,this.locale,this.from,this.to,{dateStyle:this.dateStyle,timeZone:this.timeZone,timeZoneName:this.timeZoneName,weekday:this.weekday,fractionalSecondDigits:this.fractionalSecondDigits})}catch(e){return void 0}}}e.observedAttributes=["timestamp","locale","from","to","date-style","time-zone-name","weekday","fractional-second-digits","time-zone"],window.customElements.define("time-format",e)}}function J(e){return"true"===e||"false"!==e&&void 0}function Q(e){if(!e)return;const d=parseInt(e,10);return isNaN(d)?void 0:d}var U=t(43010);function ee(e){const{locale:d,timestamp:t,from:u,to:n,weekday:r,timeZone:i,timeZoneName:a,dateStyle:c,fractionalSecondDigits:s}=e,l=new Date(t),f=l.valueOf(),m=isNaN(f)?void 0:l.toISOString();return(0,U.useIsomorphicLayoutEffect)(q,[]),o.createElement("time",{dateTime:m},o.createElement("time-format",{locale:d,timestamp:f,from:u,to:n,"time-zone":i,"time-zone-name":a,"date-style":c,"fractional-second-digits":s,weekday:r}))}var de=t(14896);const te=M.enabled("items_favoriting");function ue(e){const{index:d,style:u}=e,{focusedItemIndex:n,items:r,onClose:a,chartWidgetCollection:s,favorites:l,handleTrackEvent:f,searchString:m,focusVisible:h,focusedActionIndex:g,onRemoveCanceled:b,returnFocus:p}=(0,K.useEnsuredContext)(Y),v=r[d],_=d===n,[y,w]=(0,o.useState)((()=>v.active())),[x,N]=(0,o.useState)(!1),C=v.url?function(e){const d=e.chartId?`/chart/${e.chartId}/`:"/chart/",t=new URL(d,location.href);return e.symbol&&t.searchParams.append("symbol",e.symbol),e.interval&&t.searchParams.append("interval",e.interval),e.style&&t.searchParams.append("style",e.style),(0,W.urlWithMobileAppParams)(t.href)}({chartId:v.url}):void 0,S=(0,o.useContext)(A.SlotContext),E=(0,o.useMemo)((()=>1e3*v.modified),[v]),D=(0,o.useMemo)((()=>(0,j.createRegExpList)(m)),[m]),R=i()(de.highlight,y&&de.active);return(0,o.useEffect)((()=>(s&&s.metaInfo.id.subscribe(k),()=>{s&&s.metaInfo.id.unsubscribe(k)})),[]),o.createElement(O.DialogContentItem,{url:C,index:d,tabIndex:-1,style:u,title:o.createElement(X.LeadingEmojiText,{text:v.name,textRender:e=>o.createElement(H.HighlightedText,{className:R,queryString:m,rules:D,text:e})
}),subtitle:o.createElement(o.Fragment,null,o.createElement(H.HighlightedText,{className:R,queryString:m,rules:D,text:v.description})," ","(",o.createElement(ee,{locale:(0,$.getLocaleIso)(),timestamp:E,from:"year",to:"minute"}),")"),onClick:function(e){0;v.openAction(!0),!1},focusVisible:h,onClickFavorite:function(){0;v.favoriteAction()},showFavorite:te,onClickRemove:async function(){if(x)return;N(!0);const e=await async function(e){const d=c.t(null,{replace:{name:e.name}},t(80439));return d}(v);N(!1),function(e,d,t,u){(0,Z.showConfirm)({text:e,onConfirm:({dialogClose:e})=>{d(),e()},onClose:()=>{t()}},u)}(e,I,b,S)},isFavorite:Boolean(l[v.id]),isFocused:_,focusedActionIndex:g,isActive:y,"aria-selected":y,getElementId:V.getElementId,"data-name":"load-chart-dialog-item"});function I(){v.deleteAction(),p(null,d)}function k(e){w(v.id===e)}}var ne=t(59064),oe=t(27830),re=t(92164),ie=t(45345),ae=t(24633),ce=t(66619),se=t(67562);function le(e){const d=(0,f.useWatchedValueReadonly)({watchedValue:ie.watchedTheme})===ae.StdTheme.Dark?ce:se;return o.createElement(re.ContentIsNotFound,{className:e.className,icon:d,description:e.description})}var fe=t(173),me=t(8692);const he={sortField:"modified",sortDirection:1},ge=function(e){const{paddingTop:d=0,paddingBottom:t=0}=e;return(0,o.forwardRef)((({style:e,...u},n)=>{const{height:r=0}=e;return o.createElement("div",{ref:n,style:{...e,height:`${((0,R.isNumber)(r)?r:parseFloat(r))+d+t}px`},...u})}))}({paddingBottom:6});function be(e){let d;try{d=(0,L.getTranslatedResolution)(e)}catch(t){d=e}return d}const pe=M.enabled("items_favoriting");function ve(e){const{onClose:d,chartWidgetCollection:u,serviceState:n}=e,[r,h]=(0,o.useState)(""),[g,b]=(0,o.useState)(r),p=(0,o.useRef)(null),[v,_]=(0,o.useState)((()=>s.getJSON("loadChartDialog.viewState",he))),w=(0,o.useRef)(null),x=(0,o.useRef)(null),S=(0,o.useRef)(null),{chartList:R,favorites:I}=(0,f.useWatchedValueReadonly)({watchedValue:n}),k=(0,o.useMemo)((()=>R.map((e=>({...e,description:`${e.symbol}, ${be(e.interval)}`})))),[R]);(0,o.useEffect)((()=>{l.CheckMobile.any()||(0,a.ensureNotNull)(w.current).focus()}),[]);const P=(0,o.useRef)();(0,o.useEffect)((()=>(P.current=setTimeout((()=>{h(g)}),300),()=>{clearTimeout(P.current)})),[g]);const F=(0,o.useCallback)((()=>!0),[]),z=(0,o.useMemo)((()=>{return(0,j.rankedSearch)({data:k.sort((e=v.sortDirection,(d,t)=>{if(!oe.showFavoriteLayouts){if(I[d.id]&&!I[t.id])return-1;if(!I[d.id]&&I[t.id])return 1}const u=0===e?1:-1;return"modified"===v.sortField?u*(d.modified-t.modified):u*(0,fe.localeCompareEmojiTitles)(d.name,t.name)})),rules:(0,j.createRegExpList)(r),queryString:r,primaryKey:"name",secondaryKey:"description"});var e}),[r,v,k,!oe.showFavoriteLayouts&&I]);(0,o.useEffect)((()=>{O()}),[g]);const{handleKeyDown:B,handleForceFocus:W,handleSearchRefBlur:M,resetFocusState:O,restoreFocusState:L,focusVisible:A,focusedItemIndex:Z,focusedActionIndex:H,onDialogClick:K,setFocusedActionIndex:X,setFocusedItemIndex:$}=(0,V.useSearchDialogKeyboardNavigation)({dialogRef:p,searchInputRef:w,contentContainerRef:S,
getNextFocusedItemIndex:function(e,d){if(null===e)return 0;return(2*z.length+d+e)%z.length},isNavigationDisabled:!z.length,scrollToFocusedItem:function(e,d){x.current?.scrollToItem(d,"smart")}}),G={focusedItemIndex:Z,items:z,onClose:d,chartWidgetCollection:u,favorites:I,handleTrackEvent:U,searchString:r,focusVisible:A,focusedActionIndex:H,onRemoveCanceled:function(){(0,a.ensureNotNull)(p.current).focus(),de(null,Z)},returnFocus:de},q=(0,o.useId)();return o.createElement(m.AdaptivePopupDialog,{ref:p,onClose:d,onClick:K,onClickOutside:d,onKeyDown:B,onForceFocus:W,isOpened:!0,className:me.dialog,title:c.t(null,void 0,t(21355)),dataName:"load-layout-dialog",render:function(){return o.createElement(o.Fragment,null,o.createElement(C.DialogSearch,{reference:w,onChange:Q,activeDescendant:null!==Z?(0,V.getElementId)(Z,H):void 0,onBlur:M,onFocus:L,placeholder:c.t(null,void 0,t(8573)),"aria-controls":q,"aria-owns":q,"aria-haspopup":"listbox"}),z.length?o.createElement(o.Fragment,null,o.createElement(E,{className:i()(!pe&&me.contentHeader)},o.createElement(D,{title:c.t(null,void 0,t(11478))}),o.createElement(y,{sortDirection:v.sortDirection,title:c.t(null,void 0,t(5191)),"data-name":"load-chart-dialog-sort-button"},o.createElement(N,{label:c.t(null,void 0,t(21329)),listSortField:v.sortField,itemSortField:"title",listSortDirection:v.sortDirection,itemSortDirection:0,onClick:ee,"data-name":"load-chart-dialog-sort-menu-item"}),o.createElement(N,{label:c.t(null,void 0,t(11324)),listSortField:v.sortField,itemSortField:"title",listSortDirection:v.sortDirection,itemSortDirection:1,onClick:ee,"data-name":"load-chart-dialog-sort-menu-item"}),o.createElement(N,{label:c.t(null,void 0,t(55108)),listSortField:v.sortField,itemSortField:"modified",listSortDirection:v.sortDirection,itemSortDirection:0,onClick:ee,"data-name":"load-chart-dialog-sort-menu-item"}),o.createElement(N,{label:c.t(null,void 0,t(75272)),listSortField:v.sortField,itemSortField:"modified",listSortDirection:v.sortDirection,itemSortDirection:1,onClick:ee,"data-name":"load-chart-dialog-sort-menu-item"}))),o.createElement(Y.Provider,{value:G},o.createElement(T,{id:q,role:"grid",scrollbar:"native",contentContainerRef:S,reference:x,itemCount:z.length,itemSize:52,className:me.contentList,onScroll:J,innerElementType:ge,itemKey:e=>(I[z[e].id]?"f_":"")+z[e].id,children:ue}))):o.createElement(le,{className:me.emptyState,description:c.t(null,void 0,t(63269))}))},forceCloseOnEsc:F});function J(){ne.globalCloseDelegate.fire()}function Q(e){const d=e.currentTarget.value;b(d)}function U(e){0}function ee(e,d){const t={sortField:e,sortDirection:d};_(t),s.setValue("loadChartDialog.viewState",JSON.stringify(t),{forceFlush:!0}),U()}function de(e,d){w.current?.focus(),X(e),$(d)}}var _e=t(29280),ye=t(87896);class we extends _e.DialogRenderer{constructor(e){super(),this._options=e}show(){this.visible().value()||(this._rootInstance=(0,ye.createReactRoot)(o.createElement(ve,{...this._options,onClose:()=>this.hide()}),this._container),this._setVisibility(!0))}hide(){this._rootInstance?.unmount(),
this._setVisibility(!1)}}},2948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},66619:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#B2B5BE" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},67562:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#131722" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},645:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M19.5 18.5h-3M21.5 13.5h-5M23.5 8.5h-7M8.5 7v13.5M4.5 16.5l4 4 4-4"/></svg>'},44563:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M19.5 18.5h-3M21.5 13.5h-5M23.5 8.5h-7M8.5 20.5V7M12.5 11l-4-4-4 4"/></svg>'},18438:(e,d,t)=>{"use strict";t.d(d,{default:()=>u});const u=function(){var e={base:"https://twemoji.maxcdn.com/v/13.0.1/",ext:".png",size:"72x72",className:"emoji",convert:{fromCodePoint:function(e){var d="string"==typeof e?parseInt(e,16):e;if(d<65536)return i(d);return i(55296+((d-=65536)>>10),56320+(1023&d))},toCodePoint:v},onerror:function(){this.parentNode&&this.parentNode.replaceChild(a(this.alt,!1),this)},parse:function(d,t){t&&"function"!=typeof t||(t={callback:t});return("string"==typeof d?h:m)(d,{callback:t.callback||s,attributes:"function"==typeof t.attributes?t.attributes:b,base:"string"==typeof t.base?t.base:e.base,ext:t.ext||e.ext,size:t.folder||(u=t.size||e.size,"number"==typeof u?u+"x"+u:u),className:t.className||e.className,onerror:t.onerror||e.onerror});var u},replace:p,test:function(e){t.lastIndex=0;var d=t.test(e);return t.lastIndex=0,d}},d={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"
},t=/(?:\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g,u=/\uFE0F/g,n=String.fromCharCode(8205),o=/[&<>'"]/g,r=/^(?:iframe|noframes|noscript|script|select|style|textarea)$/,i=String.fromCharCode
;return e;function a(e,d){return document.createTextNode(d?e.replace(u,""):e)}function c(e){return e.replace(o,g)}function s(e,d){return"".concat(d.base,d.size,"/",e,d.ext)}function l(e,d){for(var t,u,n=e.childNodes,o=n.length;o--;)3===(u=(t=n[o]).nodeType)?d.push(t):1!==u||"ownerSVGElement"in t||r.test(t.nodeName.toLowerCase())||l(t,d);return d}function f(e){return v(e.indexOf(n)<0?e.replace(u,""):e)}function m(e,d){for(var u,n,o,r,i,c,s,m,h,g,b,p,v,_=l(e,[]),y=_.length;y--;){for(o=!1,r=document.createDocumentFragment(),c=(i=_[y]).nodeValue,m=0;s=t.exec(c);){if((h=s.index)!==m&&r.appendChild(a(c.slice(m,h),!0)),p=f(b=s[0]),m=h+b.length,v=d.callback(p,d),p&&v){for(n in(g=new Image).onerror=d.onerror,g.setAttribute("draggable","false"),u=d.attributes(b,p))u.hasOwnProperty(n)&&0!==n.indexOf("on")&&!g.hasAttribute(n)&&g.setAttribute(n,u[n]);g.className=d.className,g.alt=b,g.src=v,o=!0,r.appendChild(g)}g||r.appendChild(a(b,!1)),g=null}o&&(m<c.length&&r.appendChild(a(c.slice(m),!0)),i.parentNode.replaceChild(r,i))}return e}function h(e,d){return p(e,(function(e){var t,u,n=e,o=f(e),r=d.callback(o,d);if(o&&r){for(u in n="<img ".concat('class="',d.className,'" ','draggable="false" ','alt="',e,'"',' src="',r,'"'),t=d.attributes(e,o))t.hasOwnProperty(u)&&0!==u.indexOf("on")&&-1===n.indexOf(" "+u+"=")&&(n=n.concat(" ",u,'="',c(t[u]),'"'));n=n.concat("/>")}return n}))}function g(e){return d[e]}function b(){return null}function p(e,d){return String(e).replace(t,d)}function v(e,d){for(var t=[],u=0,n=0,o=0;o<e.length;)u=e.charCodeAt(o++),n?(t.push((65536+(n-55296<<10)+(u-56320)).toString(16)),n=0):55296<=u&&u<=56319?n=u:t.push(u.toString(16));return t.join(d||"-")}}()},86240:e=>{"use strict";e.exports=JSON.parse('{"size-header-height":"64px","media-phone-vertical":"all and (max-width: 479px)","media-mf-phone-landscape":"all and (min-width: 568px)"}')}}]);