(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[207],{50151:(e,t)=>{"use strict";function n(e,t){if(void 0===e)throw new Error("".concat(null!=t?t:"Value"," is undefined"));return e}function r(e,t){if(null===e)throw new Error("".concat(null!=t?t:"Value"," is null"));return e}Object.defineProperty(t,"__esModule",{value:!0}),t.ensureNever=t.ensure=t.ensureNotNull=t.ensureDefined=t.assert=void 0,t.assert=function(e,t){if(!e)throw new Error("Assertion failed".concat(t?": ".concat(t):""))},t.ensureDefined=n,t.ensureNotNull=r,t.ensure=function(e,t){return r(n(e,t),t)},t.ensureNever=function(e){}},50335:(e,t)=>{"use strict";function n(e){return Math.round(1e10*e)/1e10}Object.defineProperty(t,"__esModule",{value:!0}),t.alignTo=t.fixComputationError=t.isNaN=t.isInteger=t.isNumber=void 0,t.isNumber=function(e){return"number"==typeof e&&isFinite(e)},t.isInteger=function(e){return"number"==typeof e&&e%1==0},t.isNaN=function(e){return!(e<=0||e>0)},t.fixComputationError=n,t.alignTo=function(e,t){var r=e/t,o=Math.floor(r),i=r-o;return i>2e-10?n(i>.5?(o+1)*t:o*t):e}},30551:(e,t)=>{"use strict";t.hasProperty=t.isObject=void 0,t.isObject=function(e){var t=typeof e;return null!==e&&("object"===t||"function"===t)},t.hasProperty=function(e,t){return t in e}},91679:(e,t)=>{"use strict";t.WatchedValue=void 0;var n=function(){function e(e,t){void 0===t&&(t={}),this._listeners=[],void 0!==e&&(this._value=e),this._onDestroy=t.onDestroy}return e.prototype.destroy=function(){this.unsubscribe(),delete this._value,delete this._readonlyInstance,this._onDestroy&&this._onDestroy()},e.prototype.value=function(){return this._value},e.prototype.setValue=function(e,t){var n=this._value===e||Number.isNaN(this._value)&&Number.isNaN(e);!t&&n||(this._value=e,this._notifyListeners())},e.prototype.subscribe=function(e,t){var n,r,o=this;if(!(null===(n=null==t?void 0:t.signal)||void 0===n?void 0:n.aborted)){if((null==t?void 0:t.callWithLast)&&void 0!==this._value){try{e(this._value)}catch(e){t.onError&&t.onError(e)}if(t.once)return}(null==t?void 0:t.signal)&&t.signal.addEventListener("abort",(function(){o.unsubscribe(e)}),{once:!0}),this._listeners.push({callback:e,signal:null==t?void 0:t.signal,once:null!==(r=null==t?void 0:t.once)&&void 0!==r&&r,onError:null==t?void 0:t.onError})}},e.prototype.unsubscribe=function(e){for(var t=this._listeners.length;t--;){e!==this._listeners[t].callback&&void 0!==e||this._listeners.splice(t,1)}},e.prototype.readonly=function(){return this._readonlyInstance||(this._readonlyInstance=new o(this)),this._readonlyInstance},e.prototype.when=function(e,t){var n,r=this;if(!e)return new Promise((function(e,t){if(void 0===r._value){var n=function(t){void 0!==t&&(e(t),r.unsubscribe(n))};r.subscribe(n,{onError:t})}else e(r._value)}));if(!(null===(n=null==t?void 0:t.signal)||void 0===n?void 0:n.aborted))if(void 0===this._value){var o=function(t){void 0!==t&&(e(t),r.unsubscribe(o))};this.subscribe(o,t)}else try{e(this._value)}catch(e){(null==t?void 0:t.onError)&&t.onError(e)}},e.prototype._notifyListeners=function(){
for(var e,t,n=0,r=this._listeners;n<r.length;n++){var o=r[n];if((o.once||(null===(e=o.signal)||void 0===e?void 0:e.aborted))&&this.unsubscribe(o.callback),!(null===(t=o.signal)||void 0===t?void 0:t.aborted))try{o.callback(this._value)}catch(e){o.onError&&o.onError(e)}}},e}();t.WatchedValue=n;var r="Using destroyed WatchedValueReadonly instance is not allowed",o=function(){function e(e){this._owner=e}return e.prototype.value=function(){var e;if(!this._owner)throw new Error(r);return null===(e=this._owner)||void 0===e?void 0:e.value()},e.prototype.destroy=function(){delete this._owner},e.prototype.subscribe=function(e,t){if(!this._owner)throw new Error(r);this._owner.subscribe(e,t)},e.prototype.unsubscribe=function(e){if(!this._owner)throw new Error(r);this._owner.unsubscribe(e)},e.prototype.when=function(e,t){if(!this._owner)throw new Error(r);return void 0!==e?this._owner.when(e,t):this._owner.when()},e}()},34026:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pointInCircle=t.pointInPolygon=t.pointInBox=t.pointInTriangle=t.pointInHalfplane=void 0;var r=n(5531);t.pointInHalfplane=function(e,t){var n=t.edge;return n.A*e.x+n.B*e.y+n.C>0===t.isPositive},t.pointInTriangle=function(e,t,n,o){var i=t.add(n).scaled(.5).add(o).scaled(.5),a=r.intersectLineSegments(t,n,i,e);return null===a&&(null===(a=r.intersectLineSegments(n,o,i,e))&&null===(a=r.intersectLineSegments(o,t,i,e)))},t.pointInBox=function(e,t){return e.x>=t.min.x&&e.x<=t.max.x&&e.y>=t.min.y&&e.y<=t.max.y},t.pointInPolygon=function(e,t){for(var n=t.length-1,r=!1,o=e.x,i=e.y,a=0;a<t.length;a++){var s=t[a],u=t[n];(s.y<i&&u.y>=i||u.y<i&&s.y>=i)&&s.x+(i-s.y)/(u.y-s.y)*(u.x-s.x)<o&&(r=!r),n=a}return r},t.pointInCircle=function(e,t,n){return(e.x-t.x)*(e.x-t.x)+(e.y-t.y)*(e.y-t.y)<=n*n}},4652:(e,t)=>{"use strict";function n(e,t,n){var r=t.subtract(e),o=n.subtract(e).dotProduct(r)/r.dotProduct(r);return{coeff:o,distance:e.addScaled(r,o).subtract(n).length()}}Object.defineProperty(t,"__esModule",{value:!0}),t.distanceToSegment=t.distanceToLine=void 0,t.distanceToLine=n,t.distanceToSegment=function(e,t,r){var o=n(e,t,r);if(0<=o.coeff&&o.coeff<=1)return o;var i=e.subtract(r).length(),a=t.subtract(r).length();return i<a?{coeff:0,distance:i}:{coeff:1,distance:a}}},5531:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.intersectPolygons=t.intersectPolygonAndHalfplane=t.intersectRayAndBox=t.intersectLineAndBox=t.intersectLineSegments=t.intersectLines=t.intersectLineSegmentAndBox=void 0;var r=n(50151),o=n(86441),i=n(4652),a=n(34026);function s(e,t){var n=e.A,r=t.A,i=e.B,a=t.B,s=e.C,u=t.C,c=n*a-r*i;if(Math.abs(c)<1e-6)return null;var l=(i*u-a*s)/c,f=(r*s-n*u)/c;return new o.Point(l,f)}function u(e,t,n,r){var o=function(e,t,n,r){var o=t.subtract(e),i=r.subtract(n),a=o.x*i.y-o.y*i.x;if(Math.abs(a)<1e-6)return null;var s=e.subtract(n);return(s.y*i.x-s.x*i.y)/a}(e,t,n,r);if(null===o)return null;var a=t.subtract(e).scaled(o).add(e),s=i.distanceToSegment(n,r,a);return Math.abs(s.distance)<1e-6?o:null}function c(e,t){
for(var n=0,r=e;n<r.length;n++){var i=r[n];if(o.equalPoints(i,t))return!1}return e.push(t),!0}function l(e,t){return!(e.length>0&&(o.equalPoints(e[e.length-1],t)||o.equalPoints(e[0],t)))&&(e.push(t),!0)}function f(e,t){for(var n=[],r=0;r<e.length;++r){var i=e[r],u=e[(r+1)%e.length],c=o.lineThroughPoints(i,u);if(a.pointInHalfplane(i,t)){if(l(n,i),!a.pointInHalfplane(u,t))null!==(f=s(c,t.edge))&&l(n,f)}else if(a.pointInHalfplane(u,t)){var f;null!==(f=s(c,t.edge))&&l(n,f)}}return n.length>=3?n:null}t.intersectLineSegmentAndBox=function(e,t){var n=e[0].x,r=e[0].y,i=e[1].x,a=e[1].y,s=t.min.x,u=t.min.y,c=t.max.x,l=t.max.y;function f(e,t,n,r,o,i){var a=0;return e<n?a|=1:e>o&&(a|=2),t<r?a|=4:t>i&&(a|=8),a}for(var d=f(n,r,s,u,c,l),_=f(i,a,s,u,c,l),h=!1,p=0;;){if(p>1e3)throw new Error("Cohen - Sutherland algorithm: infinity loop");if(p++,!(d|_)){h=!0;break}if(d&_)break;var b=d||_,v=void 0,g=void 0;8&b?(v=n+(i-n)*(l-r)/(a-r),g=l):4&b?(v=n+(i-n)*(u-r)/(a-r),g=u):2&b?(g=r+(a-r)*(c-n)/(i-n),v=c):(g=r+(a-r)*(s-n)/(i-n),v=s),b===d?d=f(n=v,r=g,s,u,c,l):_=f(i=v,a=g,s,u,c,l)}return h?o.equalPoints(o.point(n,r),o.point(i,a))?o.point(n,r):o.lineSegment(o.point(n,r),o.point(i,a)):null},t.intersectLines=s,t.intersectLineSegments=u,t.intersectLineAndBox=function(e,t){var n=t.min.x,i=t.min.y,a=t.max.x,s=t.max.y;if(0===e.A){var u=-e.C/e.B;return i<=u&&u<=s?o.lineSegment(o.point(n,u),o.point(a,u)):null}if(0===e.B){var l=-e.C/e.A;return n<=l&&l<=a?o.lineSegment(o.point(l,i),o.point(l,s)):null}var f=[],d=function(t){var n=function(e,t){return-(e.C+e.A*t)/e.B}(e,t);i<=n&&n<=s&&c(f,new o.Point(t,n))},_=function(t){var r=function(e,t){return-(e.C+e.B*t)/e.A}(e,t);n<=r&&r<=a&&c(f,new o.Point(r,t))};switch(d(n),_(i),d(a),_(s),f.length){case 0:return null;case 1:return f[0];case 2:return o.equalPoints(f[0],f[1])?f[0]:o.lineSegment(f[0],f[1])}return r.assert(!1,"We should have at most two intersection points"),null},t.intersectRayAndBox=function(e,t,n){var r=u(e,t,n.min,new o.Point(n.max.x,n.min.y)),i=u(e,t,new o.Point(n.max.x,n.min.y),n.max),s=u(e,t,n.max,new o.Point(n.min.x,n.max.y)),c=u(e,t,new o.Point(n.min.x,n.max.y),n.min),l=[];if(null!==r&&r>=0&&l.push(r),null!==i&&i>=0&&l.push(i),null!==s&&s>=0&&l.push(s),null!==c&&c>=0&&l.push(c),0===l.length)return null;l.sort((function(e,t){return e-t}));var f=a.pointInBox(e,n)?l[0]:l[l.length-1];return e.addScaled(t.subtract(e),f)},t.intersectPolygonAndHalfplane=f,t.intersectPolygons=function(e,t){for(var n=e,r=0;r<t.length&&null!==n;++r){var i=t[r],a=t[(r+1)%t.length],s=t[(r+2)%t.length],u=o.lineThroughPoints(i,a);n=f(n,o.halfplaneThroughPoint(u,s))}return n}},86441:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.equalBoxes=t.box=t.halfplaneThroughPoint=t.halfplane=t.lineSegment=t.lineThroughPoints=t.line=t.equalPoints=t.point=t.Point=void 0;var n=function(){function e(e,t){this.x=e,this.y=t}return e.prototype.add=function(t){return new e(this.x+t.x,this.y+t.y)},e.prototype.addScaled=function(t,n){return new e(this.x+n*t.x,this.y+n*t.y)},e.prototype.subtract=function(t){
return new e(this.x-t.x,this.y-t.y)},e.prototype.dotProduct=function(e){return this.x*e.x+this.y*e.y},e.prototype.crossProduct=function(e){return this.x*e.y-this.y*e.x},e.prototype.signedAngle=function(e){return Math.atan2(this.crossProduct(e),this.dotProduct(e))},e.prototype.angle=function(e){return Math.acos(this.dotProduct(e)/(this.length()*e.length()))},e.prototype.length=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.scaled=function(t){return new e(this.x*t,this.y*t)},e.prototype.normalized=function(){return this.scaled(1/this.length())},e.prototype.transposed=function(){return new e(-this.y,this.x)},e.prototype.clone=function(){return new e(this.x,this.y)},e}();function r(e,t){return new n(e,t)}function o(e,t){return e.x===t.x&&e.y===t.y}function i(e,t,n){if(0===e&&0===t)throw new Error("A and B can not be both equal to zero.");return{A:e,B:t,C:n}}function a(e,t){return{edge:e,isPositive:t}}t.Point=n,t.point=r,t.equalPoints=o,t.line=i,t.lineThroughPoints=function(e,t){if(o(e,t))throw new Error("Points should be distinct");return i(e.y-t.y,t.x-e.x,e.x*t.y-t.x*e.y)},t.lineSegment=function(e,t){if(o(e,t))throw new Error("Points of a segment should be distinct");return[e,t]},t.halfplane=a,t.halfplaneThroughPoint=function(e,t){return a(e,e.A*t.x+e.B*t.y+e.C>0)},t.box=function(e,t){return{min:r(Math.min(e.x,t.x),Math.min(e.y,t.y)),max:r(Math.max(e.x,t.x),Math.max(e.y,t.y))}},t.equalBoxes=function(e,t){return o(e.min,t.min)&&o(e.max,t.max)}},24377:(e,t,n)=>{"use strict";var r=n(50335);function o(e,t,n){return r.isNaN(t)||t<e?e:t>n?n:Math.round(t)}function i(e,t,n){return r.isNaN(t)||t<e?e:t>n?n:Math.round(1e4*t)/1e4}function a(e){return o(0,e,255)}function s(e){return o(0,e,255)}function u(e){return o(0,e,255)}function c(e){return i(0,e,1)}function l(e){return i(0,e,1)}function f(e){return i(0,e,1)}function d(e){return i(0,e,1)}function _(e){return i(0,e,1)}function h(e){return i(0,e,1)}function p(e){var t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=0,s=0,u=(o+i)/2;if(o===i)a=0,s=0;else{var c=i-o;switch(s=u>.5?c/(2-i-o):c/(i+o),i){case t:a=((n-r)/c+(n<r?6:0))/6;break;case n:a=((r-t)/c+2)/6;break;case r:a=((t-n)/c+4)/6}}return[a,s,u]}function b(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function v(e){var t,n,r,o=e[0],i=e[1],c=e[2];if(0===i)t=n=r=c;else{var l=c<.5?c*(1+i):c+i-c*i,f=2*c-l;t=b(f,l,o+1/3),n=b(f,l,o),r=b(f,l,o-1/3)}return[a(255*t),s(255*n),u(255*r)]}t.normalizeAlphaComponent=c,t.areEqualRgb=function(e,t){return e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]},t.rgba=function(e,t,n,r){if(Array.isArray(e)){var o=e;return r=t,[o[0],o[1],o[2],c(r)]}var i=t;return n=n||0,r=r||0,[a(e),s(i),u(n),c(r)]},t.areEqualRgba=function(e,t){return e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},t.rgbToHsl=p,t.hslToRgb=v;var g=[.199,.687,.114];function m(e){return g[0]*e[0]+g[1]*e[1]+g[2]*e[2]}function y(e,t,n){void 0===n&&(n=.05);var r=p(e),o=r[0]+t*n;return r[0]=l(o-Math.floor(o)),v(r)}function w(e,t,n){void 0===n&&(n=.05)
;var r=e[0],o=e[1],i=e[2],a=e[3],s=y([r,o,i],t,n);return[s[0],s[1],s[2],a]}t.distanceRgb=function(e,t){var n=e[0],r=e[1],o=e[2],i=t[0]-n,a=t[1]-r,s=t[2]-o;return Math.sqrt(i*i+a*a+s*s)},t.invertRgb=function(e){return[255-e[0],255-e[1],255-e[2]]},t.blendRgba=function(e,t){var n=e[0],r=e[1],o=e[2],i=e[3],l=t[0],f=t[1],d=t[2],_=t[3],h=c(1-(1-_)*(1-i));return[a(l*_/h+n*i*(1-_)/h),s(f*_/h+r*i*(1-_)/h),u(d*_/h+o*i*(1-_)/h),h]},t.shiftRgb=y,t.shiftRgba=w,t.shiftColor=function(e,t,n){return void 0===n&&(n=.05),L(w(B(e),t,n))};var x,j,E,S,O={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",feldspar:"#d19275",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslateblue:"#8470ff",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",
skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",violetred:"#d02090",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function z(e,t){return t in e}function A(e){var t=x.re.exec(e);return null!==t?x.parse(t):null}function P(e){var t=j.re.exec(e);return null!==t?j.parse(t):null}function k(e){var t=E.re.exec(e);return null!==t?E.parse(t):null}function R(e){var t=S.re.exec(e);return null!==t?S.parse(t):null}function L(e){return"rgba("+e[0]+", "+e[1]+", "+e[2]+", "+e[3]+")"}function C(e){if(e=e.toLowerCase(),z(O,e)){var t=P(O[e]);if(null!==t)return t;throw new Error("Invalid named color definition")}var n=A(e);if(null!==n)return n;var r=P(e);if(null!==r)return r;var o=k(e);if(null!==o)return o;var i=R(e);return null!==i?[i[0],i[1],i[2]]:null}function N(e){if(e=e.toLowerCase(),z(O,e)){var t=P(O[e]);if(null!==t)return[t[0],t[1],t[2],1];throw new Error("Invalid named color definition")}var n=A(e);if(null!==n)return[n[0],n[1],n[2],1];var r=P(e);if(null!==r)return[r[0],r[1],r[2],1];var o=k(e);if(null!==o)return[o[0],o[1],o[2],1];var i=R(e);return null!==i?i:null}function B(e){var t=N(e);if(null!==t)return t;throw new Error("Passed color string does not match any of the known color representations")}!function(e){e.re=/^rgb\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*\)$/,e.parse=function(e){return[a(parseInt(e[1],10)),s(parseInt(e[2],10)),u(parseInt(e[3],10))]}}(x||(x={})),function(e){e.re=/^#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,e.parse=function(e){return[a(parseInt(e[1],16)),s(parseInt(e[2],16)),u(parseInt(e[3],16))]}}(j||(j={})),t.rgbToHexString=function(e){var t=e[0],n=e[1],r=e[2],o=t.toString(16),i=n.toString(16),a=r.toString(16);return"#"+(1===o.length?"0":"")+o+(1===i.length?"0":"")+i+(1===a.length?"0":"")+a},function(e){e.re=/^#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])$/,e.parse=function(e){return[a(parseInt(e[1]+e[1],16)),s(parseInt(e[2]+e[2],16)),u(parseInt(e[3]+e[3],16))]}}(E||(E={})),function(e){e.re=/^rgba\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?[\d]{0,10}(?:\.\d+)?)\s*\)$/,e.parse=function(e){return[a(parseInt(e[1],10)),s(parseInt(e[2],10)),u(parseInt(e[3],10)),c(parseFloat(e[4]))]}}(S||(S={})),t.rgbaToString=L,t.rgbToBlackWhiteString=function(e,t){if(t<0||t>255)throw new Error("invalid threshold value, valid values are [0, 255]");return m(e)>=t?"white":"black"},t.parseRgb=function(e){var t=C(e);if(null!==t)return t;throw new Error("Passed color string does not match any of the known color representations")},t.tryParseRgba=N,t.parseRgba=B},60521:function(e,t,n){var r;!function(){"use strict";var o,i=1e6,a=1e6,s="[big.js] ",u=s+"Invalid ",c=u+"decimal places",l=u+"rounding mode",f=s+"Division by zero",d={},_=void 0,h=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function p(e,t,n,r){var o=e.c;if(n===_&&(n=e.constructor.RM),0!==n&&1!==n&&2!==n&&3!==n)throw Error(l)
;if(t<1)r=3===n&&(r||!!o[0])||0===t&&(1===n&&o[0]>=5||2===n&&(o[0]>5||5===o[0]&&(r||o[1]!==_))),o.length=1,r?(e.e=e.e-t+1,o[0]=1):o[0]=e.e=0;else if(t<o.length){if(r=1===n&&o[t]>=5||2===n&&(o[t]>5||5===o[t]&&(r||o[t+1]!==_||1&o[t-1]))||3===n&&(r||!!o[0]),o.length=t--,r)for(;++o[t]>9;)o[t]=0,t--||(++e.e,o.unshift(1));for(t=o.length;!o[--t];)o.pop()}return e}function b(e,t,n){var r=e.e,o=e.c.join(""),i=o.length;if(t)o=o.charAt(0)+(i>1?"."+o.slice(1):"")+(r<0?"e":"e+")+r;else if(r<0){for(;++r;)o="0"+o;o="0."+o}else if(r>0)if(++r>i)for(r-=i;r--;)o+="0";else r<i&&(o=o.slice(0,r)+"."+o.slice(r));else i>1&&(o=o.charAt(0)+"."+o.slice(1));return e.s<0&&n?"-"+o:o}d.abs=function(){var e=new this.constructor(this);return e.s=1,e},d.cmp=function(e){var t,n=this,r=n.c,o=(e=new n.constructor(e)).c,i=n.s,a=e.s,s=n.e,u=e.e;if(!r[0]||!o[0])return r[0]?i:o[0]?-a:0;if(i!=a)return i;if(t=i<0,s!=u)return s>u^t?1:-1;for(a=(s=r.length)<(u=o.length)?s:u,i=-1;++i<a;)if(r[i]!=o[i])return r[i]>o[i]^t?1:-1;return s==u?0:s>u^t?1:-1},d.div=function(e){var t=this,n=t.constructor,r=t.c,o=(e=new n(e)).c,a=t.s==e.s?1:-1,s=n.DP;if(s!==~~s||s<0||s>i)throw Error(c);if(!o[0])throw Error(f);if(!r[0])return e.s=a,e.c=[e.e=0],e;var u,l,d,h,b,v=o.slice(),g=u=o.length,m=r.length,y=r.slice(0,u),w=y.length,x=e,j=x.c=[],E=0,S=s+(x.e=t.e-e.e)+1;for(x.s=a,a=S<0?0:S,v.unshift(0);w++<u;)y.push(0);do{for(d=0;d<10;d++){if(u!=(w=y.length))h=u>w?1:-1;else for(b=-1,h=0;++b<u;)if(o[b]!=y[b]){h=o[b]>y[b]?1:-1;break}if(!(h<0))break;for(l=w==u?o:v;w;){if(y[--w]<l[w]){for(b=w;b&&!y[--b];)y[b]=9;--y[b],y[w]+=10}y[w]-=l[w]}for(;!y[0];)y.shift()}j[E++]=h?d:++d,y[0]&&h?y[w]=r[g]||0:y=[r[g]]}while((g++<m||y[0]!==_)&&a--);return j[0]||1==E||(j.shift(),x.e--,S--),E>S&&p(x,S,n.RM,y[0]!==_),x},d.eq=function(e){return 0===this.cmp(e)},d.gt=function(e){return this.cmp(e)>0},d.gte=function(e){return this.cmp(e)>-1},d.lt=function(e){return this.cmp(e)<0},d.lte=function(e){return this.cmp(e)<1},d.minus=d.sub=function(e){var t,n,r,o,i=this,a=i.constructor,s=i.s,u=(e=new a(e)).s;if(s!=u)return e.s=-u,i.plus(e);var c=i.c.slice(),l=i.e,f=e.c,d=e.e;if(!c[0]||!f[0])return f[0]?e.s=-u:c[0]?e=new a(i):e.s=1,e;if(s=l-d){for((o=s<0)?(s=-s,r=c):(d=l,r=f),r.reverse(),u=s;u--;)r.push(0);r.reverse()}else for(n=((o=c.length<f.length)?c:f).length,s=u=0;u<n;u++)if(c[u]!=f[u]){o=c[u]<f[u];break}if(o&&(r=c,c=f,f=r,e.s=-e.s),(u=(n=f.length)-(t=c.length))>0)for(;u--;)c[t++]=0;for(u=t;n>s;){if(c[--n]<f[n]){for(t=n;t&&!c[--t];)c[t]=9;--c[t],c[n]+=10}c[n]-=f[n]}for(;0===c[--u];)c.pop();for(;0===c[0];)c.shift(),--d;return c[0]||(e.s=1,c=[d=0]),e.c=c,e.e=d,e},d.mod=function(e){var t,n=this,r=n.constructor,o=n.s,i=(e=new r(e)).s;if(!e.c[0])throw Error(f);return n.s=e.s=1,t=1==e.cmp(n),n.s=o,e.s=i,t?new r(n):(o=r.DP,i=r.RM,r.DP=r.RM=0,n=n.div(e),r.DP=o,r.RM=i,this.minus(n.times(e)))},d.plus=d.add=function(e){var t,n,r,o=this,i=o.constructor;if(e=new i(e),o.s!=e.s)return e.s=-e.s,o.minus(e);var a=o.e,s=o.c,u=e.e,c=e.c;if(!s[0]||!c[0])return c[0]||(s[0]?e=new i(o):e.s=o.s),e;if(s=s.slice(),t=a-u){for(t>0?(u=a,
r=c):(t=-t,r=s),r.reverse();t--;)r.push(0);r.reverse()}for(s.length-c.length<0&&(r=c,c=s,s=r),t=c.length,n=0;t;s[t]%=10)n=(s[--t]=s[t]+c[t]+n)/10|0;for(n&&(s.unshift(n),++u),t=s.length;0===s[--t];)s.pop();return e.c=s,e.e=u,e},d.pow=function(e){var t=this,n=new t.constructor("1"),r=n,o=e<0;if(e!==~~e||e<-1e6||e>a)throw Error(u+"exponent");for(o&&(e=-e);1&e&&(r=r.times(t)),e>>=1;)t=t.times(t);return o?n.div(r):r},d.prec=function(e,t){if(e!==~~e||e<1||e>i)throw Error(u+"precision");return p(new this.constructor(this),e,t)},d.round=function(e,t){if(e===_)e=0;else if(e!==~~e||e<-i||e>i)throw Error(c);return p(new this.constructor(this),e+this.e+1,t)},d.sqrt=function(){var e,t,n,r=this,o=r.constructor,i=r.s,a=r.e,u=new o("0.5");if(!r.c[0])return new o(r);if(i<0)throw Error(s+"No square root");0===(i=Math.sqrt(r+""))||i===1/0?((t=r.c.join("")).length+a&1||(t+="0"),a=((a+1)/2|0)-(a<0||1&a),e=new o(((i=Math.sqrt(t))==1/0?"5e":(i=i.toExponential()).slice(0,i.indexOf("e")+1))+a)):e=new o(i+""),a=e.e+(o.DP+=4);do{n=e,e=u.times(n.plus(r.div(n)))}while(n.c.slice(0,a).join("")!==e.c.slice(0,a).join(""));return p(e,(o.DP-=4)+e.e+1,o.RM)},d.times=d.mul=function(e){var t,n=this,r=n.constructor,o=n.c,i=(e=new r(e)).c,a=o.length,s=i.length,u=n.e,c=e.e;if(e.s=n.s==e.s?1:-1,!o[0]||!i[0])return e.c=[e.e=0],e;for(e.e=u+c,a<s&&(t=o,o=i,i=t,c=a,a=s,s=c),t=new Array(c=a+s);c--;)t[c]=0;for(u=s;u--;){for(s=0,c=a+u;c>u;)s=t[c]+i[u]*o[c-u-1]+s,t[c--]=s%10,s=s/10|0;t[c]=s}for(s?++e.e:t.shift(),u=t.length;!t[--u];)t.pop();return e.c=t,e},d.toExponential=function(e,t){var n=this,r=n.c[0];if(e!==_){if(e!==~~e||e<0||e>i)throw Error(c);for(n=p(new n.constructor(n),++e,t);n.c.length<e;)n.c.push(0)}return b(n,!0,!!r)},d.toFixed=function(e,t){var n=this,r=n.c[0];if(e!==_){if(e!==~~e||e<0||e>i)throw Error(c);for(e=e+(n=p(new n.constructor(n),e+n.e+1,t)).e+1;n.c.length<e;)n.c.push(0)}return b(n,!1,!!r)},d.toJSON=d.toString=function(){var e=this,t=e.constructor;return b(e,e.e<=t.NE||e.e>=t.PE,!!e.c[0])},d.toNumber=function(){var e=Number(b(this,!0,!0));if(!0===this.constructor.strict&&!this.eq(e.toString()))throw Error(s+"Imprecise conversion");return e},d.toPrecision=function(e,t){var n=this,r=n.constructor,o=n.c[0];if(e!==_){if(e!==~~e||e<1||e>i)throw Error(u+"precision");for(n=p(new r(n),e,t);n.c.length<e;)n.c.push(0)}return b(n,e<=n.e||n.e<=r.NE||n.e>=r.PE,!!o)},d.valueOf=function(){var e=this,t=e.constructor;if(!0===t.strict)throw Error(s+"valueOf disallowed");return b(e,e.e<=t.NE||e.e>=t.PE,!0)},o=function e(){function t(n){var r=this;if(!(r instanceof t))return n===_?e():new t(n);if(n instanceof t)r.s=n.s,r.e=n.e,r.c=n.c.slice();else{if("string"!=typeof n){if(!0===t.strict)throw TypeError(u+"number");n=0===n&&1/n<0?"-0":String(n)}!function(e,t){var n,r,o;if(!h.test(t))throw Error(u+"number");e.s="-"==t.charAt(0)?(t=t.slice(1),-1):1,(n=t.indexOf("."))>-1&&(t=t.replace(".",""));(r=t.search(/e/i))>0?(n<0&&(n=r),n+=+t.slice(r+1),t=t.substring(0,r)):n<0&&(n=t.length);for(o=t.length,r=0;r<o&&"0"==t.charAt(r);)++r;if(r==o)e.c=[e.e=0];else{
for(;o>0&&"0"==t.charAt(--o););for(e.e=n-r-1,e.c=[],n=0;r<=o;)e.c[n++]=+t.charAt(r++)}}(r,n)}r.constructor=t}return t.prototype=d,t.DP=20,t.RM=1,t.NE=-7,t.PE=21,t.strict=false,t.roundDown=0,t.roundHalfUp=1,t.roundHalfEven=2,t.roundUp=3,t}(),o.default=o.Big=o,void 0===(r=function(){return o}.call(t,n,t,e))||(e.exports=r)}()},64531:(e,t)=>{"use strict";var n,r=!("undefined"==typeof window||!window.document||!window.document.createElement);function o(){if(n)return n;if(!r||!window.document.body)return"indeterminate";var e=window.document.createElement("div");return e.appendChild(document.createTextNode("ABCD")),e.dir="rtl",e.style.fontSize="14px",e.style.width="4px",e.style.height="1px",e.style.position="absolute",e.style.top="-1000px",e.style.overflow="scroll",document.body.appendChild(e),n="reverse",e.scrollLeft>0?n="default":(e.scrollLeft=1,0===e.scrollLeft&&(n="negative")),document.body.removeChild(e),n}t.detectScrollType=o,t.getNormalizedScrollLeft=function(e,t){var n=e.scrollLeft;if("rtl"!==t)return n;var r=o();if("indeterminate"===r)return Number.NaN;switch(r){case"negative":return e.scrollWidth-e.clientWidth+n;case"reverse":return e.scrollWidth-e.clientWidth-n}return n}},32563:(e,t,n)=>{"use strict";n.d(t,{mobiletouch:()=>o,setClasses:()=>a,touch:()=>i});var r=n(75774);const o=r.mobiletouch,i=r.touch;function a(){document.documentElement.classList.add(r.touch?"feature-touch":"feature-no-touch",r.mobiletouch?"feature-mobiletouch":"feature-no-mobiletouch")}},49483:(e,t,n)=>{"use strict";n.r(t),n.d(t,{CheckMobile:()=>d,appVersion:()=>f,checkPageType:()=>p,desktopAppVersion:()=>l,isChrome:()=>b,isDesktopApp:()=>u,isEdge:()=>g,isFF:()=>v,isLinux:()=>s,isMac:()=>i,isSafari:()=>m,isSymphonyEmbed:()=>c,isWindows:()=>a,onGoPro:()=>y,onMainPage:()=>w,onWidget:()=>_,supportTouch:()=>h});var r=n(75774);const o=window.TradingView=window.TradingView||{};function i(){return r.isMac}function a(){return r.isWindows}function s(){return r.isLinux}function u(){return/TVDesktop/i.test(navigator.userAgent)}function c(){return o.isSymphony||!1}function l(){const e=navigator.userAgent.match(/TVDesktop\/([^\s]+)/);return e&&e[1]}function f(){const e=navigator.userAgent.match(/TradingView\/([^\s]+)/);return e&&e[1]}const d={Android:()=>r.isAndroid,BlackBerry:()=>r.isBlackBerry,iOS:()=>r.isIOS,Opera:()=>r.isOperaMini,isIPad:()=>r.isIPad,any:()=>r.isAnyMobile};function _(){
const e=["^widgetembed/?$","^cmewidgetembed/?$","^([0-9a-zA-Z-]+)/widgetembed/?$","^([0-9a-zA-Z-]+)/widgetstatic/?$","^([0-9a-zA-Z-]+)?/?mediumwidgetembed/?$","^twitter-chart/?$","^embed/([0-9a-zA-Z]{8})/?$","^widgetpopup/?$","^extension/?$","^idea-popup/?$","^hotlistswidgetembed/?$","^([0-9a-zA-Z-]+)/hotlistswidgetembed/?$","^marketoverviewwidgetembed/?$","^([0-9a-zA-Z-]+)/marketoverviewwidgetembed/?$","^eventswidgetembed/?$","^tickerswidgetembed/?$","^forexcrossrateswidgetembed/?$","^forexheatmapwidgetembed/?$","^marketquoteswidgetembed/?$","^screenerwidget/?$","^cryptomktscreenerwidget/?$","^([0-9a-zA-Z-]+)/cryptomktscreenerwidget/?$","^([0-9a-zA-Z-]+)/marketquoteswidgetembed/?$","^technical-analysis-widget-embed/$","^singlequotewidgetembed/?$","^([0-9a-zA-Z-]+)/singlequotewidgetembed/?$","^embed-widget/([0-9a-zA-Z-]+)/(([0-9a-zA-Z-]+)/)?$"],t=window.location.pathname.replace(/^\//,"");let n;for(let r=e.length-1;r>=0;r--)if(n=new RegExp(e[r]),n.test(t))return!0;return!1}function h(){return r.mobiletouch||r.touch||r.isAnyMobile}function p(e){return new URLSearchParams(window.location.search).get("page_type")===e}o.isMobile=d,o.onWidget=_;const b=r.isChrome,v=r.isFF,g=r.isEdge,m=r.isSafari;function y(){return"/pricing/"===window.location.pathname}function w(){return"/"===window.location.pathname}},11542:(e,t,n)=>{"use strict";n.r(t),n.d(t,{t:()=>r.t,withTranslationContext:()=>o});n(21251);var r=n(7029);function o(e){throw new Error("Not implemented")}},28865:(e,t,n)=>{"use strict";n.d(t,{getIsoLanguageCodeFromLanguage:()=>o});const r={ar_AE:"ar",br:"pt",de_DE:"de",ca_ES:"ca",he_IL:"he",id_ID:"id",in:"en",kr:"ko",ms_MY:"ms",sv_SE:"sv",th_TH:"th",uk:"en",vi_VN:"vi",zh_CN:"zh-Hans",zh_TW:"zh-Hant",zh:"zh-Hans",hu_HU:"hu-HU"};function o(e){return r[e]||e}},87795:e=>{"use strict";const t=55296,n=127995,r=127999,o=[776,2359,2359,2367,2367,2984,3007,3021,3633,3635,3648,3657,4352,4449,4520];function i(e){if("string"!=typeof e)throw new Error("string cannot be undefined or null");const t=[];let n=0,r=0;for(;n<e.length;)r+=a(n+r,e),l(e[n+r])&&r++,u(e[n+r])&&r++,c(e[n+r])&&r++,f(e[n+r])?r++:(t.push(e.substring(n,n+r)),n+=r,r=0);return t}function a(e,o){const i=o[e];if(!function(e){return e&&_(e[0].charCodeAt(0),t,56319)}(i)||e===o.length-1)return 1;const a=i+o[e+1];let u=o.substring(e+2,e+5);return s(a)&&s(u)||function(e){return _(d(e),n,r)}(u)?4:2}function s(e){return _(d(e),127462,127487)}function u(e){return"string"==typeof e&&_(e.charCodeAt(0),65024,65039)}function c(e){return"string"==typeof e&&_(e.charCodeAt(0),8400,8447)}function l(e){return"string"==typeof e&&-1!==o.indexOf(e.charCodeAt(0))}function f(e){return"string"==typeof e&&8205===e.charCodeAt(0)}function d(e){return(e.charCodeAt(0)-t<<10)+(e.charCodeAt(1)-56320)+65536}function _(e,t,n){return e>=t&&e<=n}e.exports=i,e.exports.substr=function(e,t,n){const r=i(e);if(void 0===t)return e;if(t>=r.length)return"";const o=r.length-t;let a=t+(void 0===n?o:n);return a>t+o&&(a=void 0),r.slice(t,a).join("")}},56570:(e,t,n)=>{"use strict";n.r(t),n.d(t,{disable:()=>f,
enable:()=>l,enabled:()=>u,getAllFeatures:()=>d,setEnabled:()=>c})
;const r=JSON.parse('{"14851":{},"custom_items_in_context_menu":{},"countdown":{},"symbol_search_parser_mixin":{},"pay_attention_to_ticker_not_symbol":{},"graying_disabled_tools_enabled":{},"update_study_formatter_on_symbol_resolve":{},"constraint_dialogs_movement":{},"phone_verification":{},"show_trading_notifications_history":{},"show_interval_dialog_on_key_press":{},"header_interval_dialog_button":{"subsets":["show_interval_dialog_on_key_press"]},"header_fullscreen_button":{},"header_symbol_search":{},"symbol_search_hot_key":{},"header_resolutions":{"subsets":["header_interval_dialog_button"]},"header_chart_type":{},"header_settings":{},"header_indicators":{},"header_compare":{},"header_undo_redo":{},"header_quick_search":{},"header_screenshot":{},"header_saveload":{},"study_on_study":{},"scales_date_format":{},"scales_time_hours_format":{},"header_widget":{"subsets":["header_widget_dom_node","header_symbol_search","header_resolutions","header_chart_type","header_settings","header_indicators","header_compare","header_undo_redo","header_quick_search","header_fullscreen_button","compare_symbol","header_screenshot"]},"legend_widget":{},"compare_symbol":{"subsets":["header_compare"]},"property_pages":{"subsets":["show_chart_property_page","chart_property_page"]},"show_chart_property_page":{},"chart_property_page":{"subsets":["chart_property_page_scales","chart_property_page_trading","chart_property_page_right_margin_editor"]},"left_toolbar":{},"right_toolbar":{},"hide_left_toolbar_by_default":{},"control_bar":{},"widget_logo":{},"timeframes_toolbar":{},"edit_buttons_in_legend":{"subsets":["show_hide_button_in_legend","format_button_in_legend","study_buttons_in_legend","delete_button_in_legend","legend_inplace_edit"]},"show_hide_button_in_legend":{},"object_tree_legend_mode":{},"format_button_in_legend":{},"study_buttons_in_legend":{},"delete_button_in_legend":{},"legend_inplace_edit":{},"broker_button":{},"buy_sell_buttons":{"subsets":["broker_button"]},"pane_context_menu":{},"scales_context_menu":{},"legend_context_menu":{},"context_menus":{"subsets":["pane_context_menu","scales_context_menu","legend_context_menu","objects_tree_context_menu"]},"items_favoriting":{},"save_chart_properties_to_local_storage":{},"use_localstorage_for_settings":{"subsets":["items_favoriting","save_chart_properties_to_local_storage"]},"handle_scale":{"subsets":["mouse_wheel_scale","pinch_scale","axis_pressed_mouse_move_scale"]},"handle_scroll":{"subsets":["mouse_wheel_scroll","pressed_mouse_move_scroll","horz_touch_drag_scroll","vert_touch_drag_scroll"]},"plain_studymarket":{},"disable_resolution_rebuild":{},"border_around_the_chart":{},"charting_library_debug_mode":{},"saveload_requires_authentication":{},"saveload_storage_customization":{},"volume_force_overlay":{},"create_volume_indicator_by_default":{},"create_volume_indicator_by_default_once":{},"saved_charts_count_restriction":{},"lean_chart_load":{},"stop_study_on_restart":{},"star_some_intervals_by_default":{},"move_logo_to_main_pane":{},"show_animated_logo":{},"link_to_tradingview":{},"logo_without_link":{},"logo_always_maximized":{},"right_bar_stays_on_scroll":{},"chart_content_overrides_by_defaults":{},"snapshot_trading_drawings":{},"allow_supported_resolutions_set_only":{},"widgetbar_tabs":{"subsets":["right_toolbar"]},"show_object_tree":{"subsets":["right_toolbar"]},"dom_widget":{"subsets":["right_toolbar"]},"collapsible_header":{},"study_templates":{},"side_toolbar_in_fullscreen_mode":{},"header_in_fullscreen_mode":{},"remove_library_container_border":{},"whotrades_auth_only":{},"support_multicharts":{},"display_market_status":{},"display_data_mode":{},"datasource_copypaste":{},"drawing_templates":{"subsets":["linetoolpropertieswidget_template_button"]},"expand_symbolsearch_items":{},"symbol_search_three_columns_exchanges":{},"symbol_search_flags":{},"symbol_search_limited_exchanges":{},"bugreport_button":{"subsets":["right_toolbar"]},"footer_publish_idea_button":{},"text_notes":{},"show_source_code":{},"symbol_info":{},"no_bars_status":{},"clear_bars_on_series_error":{},"hide_loading_screen_on_series_error":{},"seconds_resolution":{},"dont_show_boolean_study_arguments":{},"hide_last_na_study_output":{},"price_scale_always_last_bar_value":{},"study_dialog_fundamentals_economy_addons":{},"uppercase_instrument_names":{},"trading_notifications":{},"chart_crosshair_menu":{},"japanese_chart_styles":{},"hide_series_legend_item":{},"hide_study_overlay_legend_item":{},"hide_study_compare_legend_item":{},"linetoolpropertieswidget_template_button":{},"use_overrides_for_overlay":{},"timezone_menu":{},"main_series_scale_menu":{},"show_login_dialog":{},"remove_img_from_rss":{},"bars_marks":{},"chart_scroll":{},"chart_zoom":{},"source_selection_markers":{},"low_density_bars":{},"end_of_period_timescale_marks":{},"open_account_manager":{},"show_order_panel_on_start":{},"order_panel":{"subsets":["order_panel_close_button","order_panel_undock","right_toolbar","order_info"]},"multiple_watchlists":{},"watchlist_import_export":{},"study_overlay_compare_legend_option":{},"mobile_app_action_open_details_webview":{},"custom_resolutions":{},"referral_program_for_widget_owners":{},"mobile_trading":{},"real_brokers":{},"no_min_chart_width":{},"lock_visible_time_range_on_resize":{},"pricescale_currency":{},"cropped_tick_marks":{},"trading_account_manager":{},"disable_sameinterval_aligning":{},"display_legend_on_all_charts":{},"chart_style_hilo":{},"chart_style_hilo_last_price":{},"pricescale_unit":{},"show_spread_operators":{},"hide_exponentiation_spread_operator":{},"hide_reciprocal_spread_operator":{},"compare_symbol_search_spread_operators":{},"studies_symbol_search_spread_operators":{},"hide_resolution_in_legend":{},"hide_unresolved_symbols_in_legend":{},"fix_left_edge":{},"study_symbol_ticker_description":{},"two_character_bar_marks_labels":{},"tick_resolution":{},"secondary_series_extend_time_scale":{},"hide_volume_ma":{},"small_no_display":{},"charting_library_single_symbol_request":{},"use_ticker_on_symbol_info_update":{},"show_zoom_and_move_buttons_on_touch":{},"hide_main_series_symbol_from_indicator_legend":{},"chart_hide_close_position_button":{},"chart_hide_close_order_button":{},"hide_price_scale_global_last_bar_value":{"subsets":["use_last_visible_bar_value_in_legend"]},"keep_object_tree_widget_in_right_toolbar":{},"show_average_close_price_line_and_label":{},"hide_image_invalid_symbol":{},"hide_object_tree_and_price_scale_exchange_label":{},"confirm_overwrite_if_chart_layout_with_name_exists":{},"determine_first_data_request_size_using_visible_range":{},"use_na_string_for_not_available_values":{},"show_last_price_and_change_only_in_series_legend":{},"legend_last_day_change":{},"iframe_loading_compatibility_mode":{},"show_percent_option_for_right_margin":{},"watchlist_context_menu":{},"accessible_keyboard_shortcuts":{},"advanced_emoji_in_titles":{},"app_phone":{},"app_tablet":{},"mobile_app_hide_replay_toolbar":{},"symbol_search_option_chain_selector":{},"tv_production":{"subsets":["advanced_emoji_in_titles","auto_enable_symbol_labels","symbol_search_parser_mixin","header_fullscreen_button","header_widget","dont_show_boolean_study_arguments","left_toolbar","right_toolbar","buy_sell_buttons","control_bar","symbol_search_hot_key","context_menus","edit_buttons_in_legend","object_tree_legend_mode","uppercase_instrument_names","use_localstorage_for_settings","saveload_requires_authentication","volume_force_overlay","saved_charts_count_restriction","create_volume_indicator_by_default","create_volume_indicator_by_default_once","charts_auto_save","save_old_chart_before_save_as","chart_content_overrides_by_defaults","alerts","header_saveload","header_layouttoggle","datasource_copypaste","show_saved_watchlists","watchlists_from_to_file","add_to_watchlist","property_pages","support_multicharts","display_market_status","display_data_mode","show_chart_warn_message","support_manage_drawings","widgetbar_tabs","study_templates","collapsible_header","drawing_templates","footer_publish_idea_button","text_notes","show_source_code","symbol_info","linetoolpropertieswidget_template_button","trading_notifications","symbol_search_three_columns_exchanges","symbol_search_flags","symbol_search_limited_exchanges","phone_verification","custom_resolutions","compare_symbol","study_on_study","japanese_chart_styles","show_login_dialog","dom_widget","bars_marks","chart_scroll","chart_zoom","show_trading_notifications_history","source_selection_markers","study_dialog_fundamentals_economy_addons","multiple_watchlists","marked_symbols","order_panel","pricescale_currency","show_animated_logo","pricescale_currency","show_object_tree","watchlist_import_export","scales_date_format","scales_time_hours_format","popup_hints","show_right_widgets_panel_by_default","compare_recent_symbols_enabled","adaptive_trading_sources","chart_style_hilo_last_price","symbol_search_option_chain_selector"]},"widget":{"subsets":["auto_enable_symbol_labels","symbol_search_parser_mixin","uppercase_instrument_names","left_toolbar","right_toolbar","control_bar","symbol_search_hot_key","context_menus","edit_buttons_in_legend","object_tree_legend_mode","use_localstorage_for_settings","saveload_requires_authentication","volume_force_overlay","create_volume_indicator_by_default","create_volume_indicator_by_default_once","dont_show_boolean_study_arguments","header_widget_dom_node","header_symbol_search","header_resolutions","header_chart_type","header_compare","header_indicators","star_some_intervals_by_default","display_market_status","display_data_mode","show_chart_warn_message","symbol_info","linetoolpropertieswidget_template_button","symbol_search_three_columns_exchanges","symbol_search_flags","symbol_search_limited_exchanges","widgetbar_tabs","compare_symbol","show_login_dialog","plain_studymarket","japanese_chart_styles","bars_marks","chart_scroll","chart_zoom","source_selection_markers","property_pages","show_right_widgets_panel_by_default","chart_style_hilo_last_price"]},"bovespa_widget":{"subsets":["widget","header_settings","linetoolpropertieswidget_template_button","compare_recent_symbols_enabled"]},"charting_library_base":{"subsets":["14851","allow_supported_resolutions_set_only","auto_enable_symbol_labels","border_around_the_chart","collapsible_header","constraint_dialogs_movement","context_menus","control_bar","create_volume_indicator_by_default","custom_items_in_context_menu","datasource_copypaste","uppercase_instrument_names","display_market_status","edit_buttons_in_legend","object_tree_legend_mode","graying_disabled_tools_enabled","header_widget","legend_widget","header_saveload","dont_show_boolean_study_arguments","lean_chart_load","left_toolbar","right_toolbar","link_to_tradingview","pay_attention_to_ticker_not_symbol","plain_studymarket","refresh_saved_charts_list_on_dialog_show","right_bar_stays_on_scroll","saveload_storage_customization","stop_study_on_restart","timeframes_toolbar","symbol_search_hot_key","update_study_formatter_on_symbol_resolve","update_timeframes_set_on_symbol_resolve","use_localstorage_for_settings","volume_force_overlay","widget_logo","countdown","use_overrides_for_overlay","trading_notifications","compare_symbol","symbol_info","timezone_menu","main_series_scale_menu","create_volume_indicator_by_default_once","bars_marks","chart_scroll","chart_zoom","source_selection_markers","property_pages","go_to_date","adaptive_logo","show_animated_logo","handle_scale","handle_scroll","shift_visible_range_on_new_bar","chart_content_overrides_by_defaults","cropped_tick_marks","scales_date_format","scales_time_hours_format","popup_hints","save_shortcut","show_right_widgets_panel_by_default","show_object_tree","insert_indicator_dialog_shortcut","compare_recent_symbols_enabled","hide_main_series_symbol_from_indicator_legend","chart_style_hilo","request_only_visible_range_on_reset","clear_price_scale_on_error_or_empty_bars","show_symbol_logo_in_legend","show_symbol_logo_for_compare_studies","library_custom_color_themes"]},"charting_library":{"subsets":["charting_library_base"]},"static_charts_service":{"subsets":["charting_library","disable_resolution_rebuild"]},"trading_terminal":{"subsets":["charting_library_base","support_multicharts","header_layouttoggle","japanese_chart_styles","chart_property_page_trading","add_to_watchlist","open_account_manager","show_dom_first_time","order_panel","buy_sell_buttons","multiple_watchlists","show_trading_notifications_history","always_pass_called_order_to_modify","show_object_tree","watchlist_import_export","drawing_templates","trading_account_manager","chart_crosshair_menu","compare_recent_symbols_enabled","adaptive_trading_sources","watchlist_context_menu","show_symbol_logo_in_account_manager","watchlist_sections","prefer_quote_short_name","enable_dom_data_for_untradable_symbols","prefer_symbol_name_over_fullname","watchlist_cross_tab_sync"]}}')
;var o=n.t(r,2);const i=new Map,a=new Map,s=new Set;function u(e){const t=i.get(e);if(void 0!==t)return t;const n=a.get(e);return!!n&&n.some(u)}function c(e,t){i.set(String(e),Boolean(t))}function l(e){c(e,!0)}function f(e){c(e,!1)}function d(){const e=Object.create(null);for(const t of s)e[t]=u(t);return e}!function(){for(const[e,t]of Object.entries(o))if(s.add(e),"subsets"in t)for(const n of t.subsets){s.add(n);let t=a.get(n);void 0===t&&(t=[],a.set(n,t)),t.push(e)}"object"==typeof __initialDisabledFeaturesets&&Array.isArray(__initialDisabledFeaturesets)&&__initialDisabledFeaturesets.forEach(f),"object"==typeof __initialEnabledFeaturesets&&Array.isArray(__initialEnabledFeaturesets)&&__initialEnabledFeaturesets.forEach(l)}()},37265:function(e,t,n){e=n.nmd(e);const{clone:r,merge:o,isFunction:i,deepEquals:a,isObject:s,isNumber:u}=n(97085);var c,l=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function f(e,t){e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}"undefined"!=typeof window?(c=window.TradingView=window.TradingView||{},window.isNumber=u,window.isFunction=i,window.inherit=f,window.isArray=l):c=this.TradingView=this.TradingView||{},c.isNaN=function(e){return!(e<=0||e>0)},c.isAbsent=function(e){return null==e},c.isExistent=function(e){return null!=e},Number.isNaN=Number.isNaN||function(e){return e!=e},c.isSameType=function(e,t){return Number.isNaN(e)||Number.isNaN(t)?Number.isNaN(e)===Number.isNaN(t):{}.toString.call(e)==={}.toString.call(t)},c.isInteger=function(e){return"number"==typeof e&&e%1==0},c.isString=function(e){return null!=e&&e.constructor===String},c.isInherited=function(e,t){if(null==e||null==e.prototype)throw new TypeError("isInherited: child should be a constructor function");if(null==t||null==t.prototype)throw new TypeError("isInherited: parent should be a constructor function");return e.prototype instanceof t||e.prototype===t.prototype},c.clone=r,c.deepEquals=a,c.merge=o,e&&e.exports&&(e.exports={inherit:f,clone:c.clone,merge:c.merge,isNumber:u,isInteger:c.isInteger,isString:c.isString,isObject:s,isHashObject:function(e){return s(e)&&-1!==e.constructor.toString().indexOf("function Object")},isPromise:function(e){return s(e)&&e.then},isNaN:c.isNaN,isAbsent:c.isAbsent,isExistent:c.isExistent,isSameType:c.isSameType,isArray:l,isFunction:i,parseBool:c.parseBool,deepEquals:a,notNull:function(e){return null!==e},notUndefined:function(e){return void 0!==e},isEven:function(e){return e%2==0},declareClassAsPureInterface:function(e,t){for(var n in e.prototype)"function"==typeof e.prototype[n]&&e.prototype.hasOwnProperty(n)&&(e.prototype[n]=function(){throw new Error(t+"::"+n+" is an interface member declaration and must be overloaded in order to be called")})},requireFullInterfaceImplementation:function(e,t,n,r){for(var o in n.prototype)if("function"==typeof n.prototype[o]&&!e.prototype[o])throw new Error("Interface implementation assertion failed: "+t+" does not implement "+r+"::"+o+" function")}})},
21251:(e,t,n)=>{"use strict";n.r(t);var r=n(37265);const o=/{(\w+)}/g,i=/{(\d+)}/g;String.prototype.format=function(...e){const t=(0,r.isObject)(e[0]),n=t?o:i,a=t?(t,n)=>{const r=e[0];return void 0!==r[n]?r[n]:t}:(t,n)=>{const r=parseInt(n,10),o=e[r];return void 0!==o?o:t};return this.replace(n,a)}},44286:()=>{"use strict";var e,t,n,r,o,i;window.parent!==window&&window.CanvasRenderingContext2D&&window.TextMetrics&&(t=window.CanvasRenderingContext2D.prototype)&&t.hasOwnProperty("font")&&t.hasOwnProperty("mozTextStyle")&&"function"==typeof t.__lookupSetter__&&(n=t.__lookupSetter__("font"))&&(t.__defineSetter__("font",(function(e){try{return n.call(this,e)}catch(e){if("NS_ERROR_FAILURE"!==e.name)throw e}})),r=t.measureText,e=function(){this.width=0,this.isFake=!0,this.__proto__=window.TextMetrics.prototype},t.measureText=function(t){try{return r.apply(this,arguments)}catch(t){if("NS_ERROR_FAILURE"!==t.name)throw t;return new e}},o=t.fillText,t.fillText=function(e,t,n,r){try{o.apply(this,arguments)}catch(e){if("NS_ERROR_FAILURE"!==e.name)throw e}},i=t.strokeText,t.strokeText=function(e,t,n,r){try{i.apply(this,arguments)}catch(e){if("NS_ERROR_FAILURE"!==e.name)throw e}})},85459:function(e,t,n){var r;!function(t){"use strict";function o(){}var i=o.prototype,a=t.EventEmitter;function s(e,t){for(var n=e.length;n--;)if(e[n].listener===t)return n;return-1}function u(e){return function(){return this[e].apply(this,arguments)}}function c(e){return"function"==typeof e||e instanceof RegExp||!(!e||"object"!=typeof e)&&c(e.listener)}i.getListeners=function(e){var t,n,r=this._getEvents();if(e instanceof RegExp)for(n in t={},r)r.hasOwnProperty(n)&&e.test(n)&&(t[n]=r[n]);else t=r[e]||(r[e]=[]);return t},i.flattenListeners=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n.push(e[t].listener);return n},i.getListenersAsObject=function(e){var t,n=this.getListeners(e);return n instanceof Array&&((t={})[e]=n),t||n},i.addListener=function(e,t){if(!c(t))throw new TypeError("listener must be a function");var n,r=this.getListenersAsObject(e),o="object"==typeof t;for(n in r)r.hasOwnProperty(n)&&-1===s(r[n],t)&&r[n].push(o?t:{listener:t,once:!1});return this},i.on=u("addListener"),i.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},i.once=u("addOnceListener"),i.defineEvent=function(e){return this.getListeners(e),this},i.defineEvents=function(e){for(var t=0;t<e.length;t+=1)this.defineEvent(e[t]);return this},i.removeListener=function(e,t){var n,r,o=this.getListenersAsObject(e);for(r in o)o.hasOwnProperty(r)&&-1!==(n=s(o[r],t))&&o[r].splice(n,1);return this},i.off=u("removeListener"),i.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},i.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},i.manipulateListeners=function(e,t,n){var r,o,i=e?this.removeListener:this.addListener,a=e?this.removeListeners:this.addListeners
;if("object"!=typeof t||t instanceof RegExp)for(r=n.length;r--;)i.call(this,t,n[r]);else for(r in t)t.hasOwnProperty(r)&&(o=t[r])&&("function"==typeof o?i.call(this,r,o):a.call(this,r,o));return this},i.removeEvent=function(e){var t,n=typeof e,r=this._getEvents();if("string"===n)delete r[e];else if(e instanceof RegExp)for(t in r)r.hasOwnProperty(t)&&e.test(t)&&delete r[t];else delete this._events;return this},i.removeAllListeners=u("removeEvent"),i.emitEvent=function(e,t){var n,r,o,i,a=this.getListenersAsObject(e);for(i in a)if(a.hasOwnProperty(i))for(n=a[i].slice(0),o=0;o<n.length;o++)!0===(r=n[o]).once&&this.removeListener(e,r.listener),r.listener.apply(this,t||[])===this._getOnceReturnValue()&&this.removeListener(e,r.listener);return this},i.trigger=u("emitEvent"),i.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return this.emitEvent(e,t)},i.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},i._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},i._getEvents=function(){return this._events||(this._events={})},o.noConflict=function(){return t.EventEmitter=a,o},void 0===(r=function(){return o}.call(t,n,t,e))||(e.exports=r)}(this||{})},27714:(e,t,n)=>{"use strict";function r(e){var t=e.width,n=e.height;if(t<0)throw new Error("Negative width is not allowed for Size");if(n<0)throw new Error("Negative height is not allowed for Size");return{width:t,height:n}}function o(e,t){return e.width===t.width&&e.height===t.height}n.d(t,{CanvasRenderingTarget2D:()=>c,bindCanvasElementBitmapSizeTo:()=>s,equalSizes:()=>o,size:()=>r});var i=function(){function e(e){var t=this;this._resolutionListener=function(){return t._onResolutionChanged()},this._resolutionMediaQueryList=null,this._observers=[],this._window=e,this._installResolutionListener()}return e.prototype.dispose=function(){this._uninstallResolutionListener(),this._window=null},Object.defineProperty(e.prototype,"value",{get:function(){return this._window.devicePixelRatio},enumerable:!1,configurable:!0}),e.prototype.subscribe=function(e){var t=this,n={next:e};return this._observers.push(n),{unsubscribe:function(){t._observers=t._observers.filter((function(e){return e!==n}))}}},e.prototype._installResolutionListener=function(){if(null!==this._resolutionMediaQueryList)throw new Error("Resolution listener is already installed");var e=this._window.devicePixelRatio;this._resolutionMediaQueryList=this._window.matchMedia("all and (resolution: ".concat(e,"dppx)")),this._resolutionMediaQueryList.addListener(this._resolutionListener)},e.prototype._uninstallResolutionListener=function(){null!==this._resolutionMediaQueryList&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},e.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},e.prototype._onResolutionChanged=function(){var e=this;this._observers.forEach((function(t){return t.next(e._window.devicePixelRatio)})),
this._reinstallResolutionListener()},e}();var a=function(){function e(e,t,n){var o;this._canvasElement=null,this._bitmapSizeChangedListeners=[],this._suggestedBitmapSize=null,this._suggestedBitmapSizeChangedListeners=[],this._devicePixelRatioObservable=null,this._canvasElementResizeObserver=null,this._canvasElement=e,this._canvasElementClientSize=r({width:this._canvasElement.clientWidth,height:this._canvasElement.clientHeight}),this._transformBitmapSize=null!=t?t:function(e){return e},this._allowResizeObserver=null===(o=null==n?void 0:n.allowResizeObserver)||void 0===o||o,this._chooseAndInitObserver()}return e.prototype.dispose=function(){var e,t;if(null===this._canvasElement)throw new Error("Object is disposed");null===(e=this._canvasElementResizeObserver)||void 0===e||e.disconnect(),this._canvasElementResizeObserver=null,null===(t=this._devicePixelRatioObservable)||void 0===t||t.dispose(),this._devicePixelRatioObservable=null,this._suggestedBitmapSizeChangedListeners.length=0,this._bitmapSizeChangedListeners.length=0,this._canvasElement=null},Object.defineProperty(e.prototype,"canvasElement",{get:function(){if(null===this._canvasElement)throw new Error("Object is disposed");return this._canvasElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"canvasElementClientSize",{get:function(){return this._canvasElementClientSize},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bitmapSize",{get:function(){return r({width:this.canvasElement.width,height:this.canvasElement.height})},enumerable:!1,configurable:!0}),e.prototype.resizeCanvasElement=function(e){this._canvasElementClientSize=r(e),this.canvasElement.style.width="".concat(this._canvasElementClientSize.width,"px"),this.canvasElement.style.height="".concat(this._canvasElementClientSize.height,"px"),this._invalidateBitmapSize()},e.prototype.subscribeBitmapSizeChanged=function(e){this._bitmapSizeChangedListeners.push(e)},e.prototype.unsubscribeBitmapSizeChanged=function(e){this._bitmapSizeChangedListeners=this._bitmapSizeChangedListeners.filter((function(t){return t!==e}))},Object.defineProperty(e.prototype,"suggestedBitmapSize",{get:function(){return this._suggestedBitmapSize},enumerable:!1,configurable:!0}),e.prototype.subscribeSuggestedBitmapSizeChanged=function(e){this._suggestedBitmapSizeChangedListeners.push(e)},e.prototype.unsubscribeSuggestedBitmapSizeChanged=function(e){this._suggestedBitmapSizeChangedListeners=this._suggestedBitmapSizeChangedListeners.filter((function(t){return t!==e}))},e.prototype.applySuggestedBitmapSize=function(){if(null!==this._suggestedBitmapSize){var e=this._suggestedBitmapSize;this._suggestedBitmapSize=null,this._resizeBitmap(e),this._emitSuggestedBitmapSizeChanged(e,this._suggestedBitmapSize)}},e.prototype._resizeBitmap=function(e){var t=this.bitmapSize;o(t,e)||(this.canvasElement.width=e.width,this.canvasElement.height=e.height,this._emitBitmapSizeChanged(t,e))},e.prototype._emitBitmapSizeChanged=function(e,t){var n=this;this._bitmapSizeChangedListeners.forEach((function(r){
return r.call(n,e,t)}))},e.prototype._suggestNewBitmapSize=function(e){var t=this._suggestedBitmapSize,n=r(this._transformBitmapSize(e,this._canvasElementClientSize)),i=o(this.bitmapSize,n)?null:n;null===t&&null===i||null!==t&&null!==i&&o(t,i)||(this._suggestedBitmapSize=i,this._emitSuggestedBitmapSizeChanged(t,i))},e.prototype._emitSuggestedBitmapSizeChanged=function(e,t){var n=this;this._suggestedBitmapSizeChangedListeners.forEach((function(r){return r.call(n,e,t)}))},e.prototype._chooseAndInitObserver=function(){var e=this;this._allowResizeObserver?new Promise((function(e){var t=new ResizeObserver((function(n){e(n.every((function(e){return"devicePixelContentBoxSize"in e}))),t.disconnect()}));t.observe(document.body,{box:"device-pixel-content-box"})})).catch((function(){return!1})).then((function(t){return t?e._initResizeObserver():e._initDevicePixelRatioObservable()})):this._initDevicePixelRatioObservable()},e.prototype._initDevicePixelRatioObservable=function(){var e=this;if(null!==this._canvasElement){var t=u(this._canvasElement);if(null===t)throw new Error("No window is associated with the canvas");this._devicePixelRatioObservable=function(e){return new i(e)}(t),this._devicePixelRatioObservable.subscribe((function(){return e._invalidateBitmapSize()})),this._invalidateBitmapSize()}},e.prototype._invalidateBitmapSize=function(){var e,t;if(null!==this._canvasElement){var n=u(this._canvasElement);if(null!==n){var o=null!==(t=null===(e=this._devicePixelRatioObservable)||void 0===e?void 0:e.value)&&void 0!==t?t:n.devicePixelRatio,i=this._canvasElement.getClientRects(),a=void 0!==i[0]?function(e,t){return r({width:Math.round(e.left*t+e.width*t)-Math.round(e.left*t),height:Math.round(e.top*t+e.height*t)-Math.round(e.top*t)})}(i[0],o):r({width:this._canvasElementClientSize.width*o,height:this._canvasElementClientSize.height*o});this._suggestNewBitmapSize(a)}}},e.prototype._initResizeObserver=function(){var e=this;null!==this._canvasElement&&(this._canvasElementResizeObserver=new ResizeObserver((function(t){var n=t.find((function(t){return t.target===e._canvasElement}));if(n&&n.devicePixelContentBoxSize&&n.devicePixelContentBoxSize[0]){var o=n.devicePixelContentBoxSize[0],i=r({width:o.inlineSize,height:o.blockSize});e._suggestNewBitmapSize(i)}})),this._canvasElementResizeObserver.observe(this._canvasElement,{box:"device-pixel-content-box"}))},e}();function s(e,t){if("device-pixel-content-box"===t.type)return new a(e,t.transform,t.options);throw new Error("Unsupported binding target")}function u(e){return e.ownerDocument.defaultView}var c=function(){function e(e,t,n){if(0===t.width||0===t.height)throw new TypeError("Rendering target could only be created on a media with positive width and height");if(this._mediaSize=t,0===n.width||0===n.height)throw new TypeError("Rendering target could only be created using a bitmap with positive integer width and height");this._bitmapSize=n,this._context=e}return e.prototype.useMediaCoordinateSpace=function(e){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),
this._context.scale(this._horizontalPixelRatio,this._verticalPixelRatio),e({context:this._context,mediaSize:this._mediaSize})}finally{this._context.restore()}},e.prototype.useBitmapCoordinateSpace=function(e){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),e({context:this._context,mediaSize:this._mediaSize,bitmapSize:this._bitmapSize,horizontalPixelRatio:this._horizontalPixelRatio,verticalPixelRatio:this._verticalPixelRatio})}finally{this._context.restore()}},Object.defineProperty(e.prototype,"_horizontalPixelRatio",{get:function(){return this._bitmapSize.width/this._mediaSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"_verticalPixelRatio",{get:function(){return this._bitmapSize.height/this._mediaSize.height},enumerable:!1,configurable:!0}),e}()},46956:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});const r=function(){this.__data__=[],this.size=0};var o=n(54523);const i=function(e,t){for(var n=e.length;n--;)if((0,o.default)(e[n][0],t))return n;return-1};var a=Array.prototype.splice;const s=function(e){var t=this.__data__,n=i(t,e);return!(n<0)&&(n==t.length-1?t.pop():a.call(t,n,1),--this.size,!0)};const u=function(e){var t=this.__data__,n=i(t,e);return n<0?void 0:t[n][1]};const c=function(e){return i(this.__data__,e)>-1};const l=function(e,t){var n=this.__data__,r=i(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function f(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}f.prototype.clear=r,f.prototype.delete=s,f.prototype.get=u,f.prototype.has=c,f.prototype.set=l;const d=f},19385:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(52494),o=n(99615);const i=(0,r.default)(o.default,"Map")},75440:(e,t,n)=>{"use strict";n.d(t,{default:()=>j});const r=(0,n(52494).default)(Object,"create");const o=function(){this.__data__=r?r(null):{},this.size=0};const i=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};var a=Object.prototype.hasOwnProperty;const s=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return a.call(t,e)?t[e]:void 0};var u=Object.prototype.hasOwnProperty;const c=function(e){var t=this.__data__;return r?void 0!==t[e]:u.call(t,e)};const l=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this};function f(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}f.prototype.clear=o,f.prototype.delete=i,f.prototype.get=s,f.prototype.has=c,f.prototype.set=l;const d=f;var _=n(46956),h=n(19385);const p=function(){this.size=0,this.__data__={hash:new d,map:new(h.default||_.default),string:new d}};const b=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};const v=function(e,t){var n=e.__data__;return b(t)?n["string"==typeof t?"string":"hash"]:n.map};const g=function(e){var t=v(this,e).delete(e);return this.size-=t?1:0,t};const m=function(e){return v(this,e).get(e)}
;const y=function(e){return v(this,e).has(e)};const w=function(e,t){var n=v(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function x(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}x.prototype.clear=p,x.prototype.delete=g,x.prototype.get=m,x.prototype.has=y,x.prototype.set=w;const j=x},87593:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(46956);const o=function(){this.__data__=new r.default,this.size=0};const i=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};const a=function(e){return this.__data__.get(e)};const s=function(e){return this.__data__.has(e)};var u=n(19385),c=n(75440);const l=function(e,t){var n=this.__data__;if(n instanceof r.default){var o=n.__data__;if(!u.default||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new c.default(o)}return n.set(e,t),this.size=n.size,this};function f(e){var t=this.__data__=new r.default(e);this.size=t.size}f.prototype.clear=o,f.prototype.delete=i,f.prototype.get=a,f.prototype.has=s,f.prototype.set=l;const d=f},66711:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=n(99615).default.Symbol},16299:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=n(99615).default.Uint8Array},60545:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});const r=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r};var o=n(54404),i=n(56052),a=n(32437),s=n(99313),u=n(9125),c=Object.prototype.hasOwnProperty;const l=function(e,t){var n=(0,i.default)(e),l=!n&&(0,o.default)(e),f=!n&&!l&&(0,a.default)(e),d=!n&&!l&&!f&&(0,u.default)(e),_=n||l||f||d,h=_?r(e.length,String):[],p=h.length;for(var b in e)!t&&!c.call(e,b)||_&&("length"==b||f&&("offset"==b||"parent"==b)||d&&("buffer"==b||"byteLength"==b||"byteOffset"==b)||(0,s.default)(b,p))||h.push(b);return h}},18573:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}},61572:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(857),o=n(54523),i=Object.prototype.hasOwnProperty;const a=function(e,t,n){var a=e[t];i.call(e,t)&&(0,o.default)(a,n)&&(void 0!==n||t in e)||(0,r.default)(e,t,n)}},857:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(55136);const o=function(e,t,n){"__proto__"==t&&r.default?(0,r.default)(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},76507:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return function(t,n,r){for(var o=-1,i=Object(t),a=r(t),s=a.length;s--;){var u=a[e?s:++o];if(!1===n(i[u],u,i))break}return t}}()},49084:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(31434),o=n(13383);const i=function(e,t){for(var n=0,i=(t=(0,r.default)(t,e)).length;null!=e&&n<i;)e=e[(0,o.default)(t[n++])];return n&&n==i?e:void 0}},96909:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(18573),o=n(56052);const i=function(e,t,n){var i=t(e);return(0,o.default)(e)?i:(0,r.default)(i,n(e))}},89572:(e,t,n)=>{"use strict";n.d(t,{default:()=>d})
;var r=n(66711),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=r.default?r.default.toStringTag:void 0;const u=function(e){var t=i.call(e,s),n=e[s];try{e[s]=void 0;var r=!0}catch(e){}var o=a.call(e);return r&&(t?e[s]=n:delete e[s]),o};var c=Object.prototype.toString;const l=function(e){return c.call(e)};var f=r.default?r.default.toStringTag:void 0;const d=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":f&&f in Object(e)?u(e):l(e)}},12189:(e,t,n)=>{"use strict";n.d(t,{default:()=>C});var r=n(87593),o=n(75440);const i=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};const a=function(e){return this.__data__.has(e)};function s(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new o.default;++t<n;)this.add(e[t])}s.prototype.add=s.prototype.push=i,s.prototype.has=a;const u=s;const c=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1};const l=function(e,t){return e.has(t)};const f=function(e,t,n,r,o,i){var a=1&n,s=e.length,f=t.length;if(s!=f&&!(a&&f>s))return!1;var d=i.get(e),_=i.get(t);if(d&&_)return d==t&&_==e;var h=-1,p=!0,b=2&n?new u:void 0;for(i.set(e,t),i.set(t,e);++h<s;){var v=e[h],g=t[h];if(r)var m=a?r(g,v,h,t,e,i):r(v,g,h,e,t,i);if(void 0!==m){if(m)continue;p=!1;break}if(b){if(!c(t,(function(e,t){if(!l(b,t)&&(v===e||o(v,e,n,r,i)))return b.push(t)}))){p=!1;break}}else if(v!==g&&!o(v,g,n,r,i)){p=!1;break}}return i.delete(e),i.delete(t),p};var d=n(66711),_=n(16299),h=n(54523);const p=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n};const b=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n};var v=d.default?d.default.prototype:void 0,g=v?v.valueOf:void 0;const m=function(e,t,n,r,o,i,a){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!i(new _.default(e),new _.default(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return(0,h.default)(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=p;case"[object Set]":var u=1&r;if(s||(s=b),e.size!=t.size&&!u)return!1;var c=a.get(e);if(c)return c==t;r|=2,a.set(e,t);var l=f(s(e),s(t),r,o,i,a);return a.delete(e),l;case"[object Symbol]":if(g)return g.call(e)==g.call(t)}return!1};var y=n(38366),w=Object.prototype.hasOwnProperty;const x=function(e,t,n,r,o,i){var a=1&n,s=(0,y.default)(e),u=s.length;if(u!=(0,y.default)(t).length&&!a)return!1;for(var c=u;c--;){var l=s[c];if(!(a?l in t:w.call(t,l)))return!1}var f=i.get(e),d=i.get(t);if(f&&d)return f==t&&d==e;var _=!0;i.set(e,t),i.set(t,e);for(var h=a;++c<u;){var p=e[l=s[c]],b=t[l];if(r)var v=a?r(b,p,l,t,e,i):r(p,b,l,e,t,i);if(!(void 0===v?p===b||o(p,b,n,r,i):v)){_=!1;break}h||(h="constructor"==l)}if(_&&!h){var g=e.constructor,m=t.constructor
;g==m||!("constructor"in e)||!("constructor"in t)||"function"==typeof g&&g instanceof g&&"function"==typeof m&&m instanceof m||(_=!1)}return i.delete(e),i.delete(t),_};var j=n(81296),E=n(56052),S=n(32437),O=n(9125),z="[object Arguments]",A="[object Array]",P="[object Object]",k=Object.prototype.hasOwnProperty;const R=function(e,t,n,o,i,a){var s=(0,E.default)(e),u=(0,E.default)(t),c=s?A:(0,j.default)(e),l=u?A:(0,j.default)(t),d=(c=c==z?P:c)==P,_=(l=l==z?P:l)==P,h=c==l;if(h&&(0,S.default)(e)){if(!(0,S.default)(t))return!1;s=!0,d=!1}if(h&&!d)return a||(a=new r.default),s||(0,O.default)(e)?f(e,t,n,o,i,a):m(e,t,c,n,o,i,a);if(!(1&n)){var p=d&&k.call(e,"__wrapped__"),b=_&&k.call(t,"__wrapped__");if(p||b){var v=p?e.value():e,g=b?t.value():t;return a||(a=new r.default),i(v,g,n,o,a)}}return!!h&&(a||(a=new r.default),x(e,t,n,o,i,a))};var L=n(13795);const C=function e(t,n,r,o,i){return t===n||(null==t||null==n||!(0,L.default)(t)&&!(0,L.default)(n)?t!=t&&n!=n:R(t,n,r,o,e,i))}},89815:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(5196);const o=(0,n(45635).default)(Object.keys,Object);var i=Object.prototype.hasOwnProperty;const a=function(e){if(!(0,r.default)(e))return o(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},38459:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(o);++r<o;)i[r]=e[r+t];return i}},5467:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return function(t){return e(t)}}},31434:(e,t,n)=>{"use strict";n.d(t,{default:()=>b});var r=n(56052),o=n(61070),i=n(59332);var a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,s=/\\(\\)?/g;const u=function(e){var t=(0,i.default)(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(a,(function(e,n,r,o){t.push(r?o.replace(s,"$1"):n||e)})),t}));var c=n(66711);const l=function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o};var f=n(98111),d=c.default?c.default.prototype:void 0,_=d?d.toString:void 0;const h=function e(t){if("string"==typeof t)return t;if((0,r.default)(t))return l(t,e)+"";if((0,f.default)(t))return _?_.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n};const p=function(e){return null==e?"":h(e)};const b=function(e,t){return(0,r.default)(e)?e:(0,o.default)(e,t)?[e]:u(p(e))}},22605:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(16299);const o=function(e){var t=new e.constructor(e.byteLength);return new r.default(t).set(new r.default(e)),t}},14054:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(99615),o="object"==typeof exports&&exports&&!exports.nodeType&&exports,i=o&&"object"==typeof module&&module&&!module.nodeType&&module,a=i&&i.exports===o?r.default.Buffer:void 0,s=a?a.allocUnsafe:void 0;const u=function(e,t){if(t)return e.slice();var n=e.length,r=s?s(n):new e.constructor(n);return e.copy(r),r}
},11523:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(22605);const o=function(e,t){var n=t?(0,r.default)(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},32126:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}},52949:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(61572),o=n(857);const i=function(e,t,n,i){var a=!n;n||(n={});for(var s=-1,u=t.length;++s<u;){var c=t[s],l=i?i(n[c],e[c],c,n,e):void 0;void 0===l&&(l=e[c]),a?(0,o.default)(n,c,l):(0,r.default)(n,c,l)}return n}},55136:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(52494);const o=function(){try{var e=(0,r.default)(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},97889:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r="object"==typeof global&&global&&global.Object===Object&&global},38366:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(96909),o=n(21578),i=n(77251);const a=function(e){return(0,r.default)(e,i.default,o.default)}},52494:(e,t,n)=>{"use strict";n.d(t,{default:()=>g});var r=n(88987);const o=n(99615).default["__core-js_shared__"];var i,a=(i=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+i:"";const s=function(e){return!!a&&a in e};var u=n(82433),c=n(65114),l=/^\[object .+?Constructor\]$/,f=Function.prototype,d=Object.prototype,_=f.toString,h=d.hasOwnProperty,p=RegExp("^"+_.call(h).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const b=function(e){return!(!(0,u.default)(e)||s(e))&&((0,r.default)(e)?p:l).test((0,c.default)(e))};const v=function(e,t){return null==e?void 0:e[t]};const g=function(e,t){var n=v(e,t);return b(n)?n:void 0}},10964:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=(0,n(45635).default)(Object.getPrototypeOf,Object)},21578:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});const r=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var a=e[n];t(a,n,e)&&(i[o++]=a)}return i};var o=n(69043),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;const s=a?function(e){return null==e?[]:(e=Object(e),r(a(e),(function(t){return i.call(e,t)})))}:o.default},81296:(e,t,n)=>{"use strict";n.d(t,{default:()=>j});var r=n(52494),o=n(99615);const i=(0,r.default)(o.default,"DataView");var a=n(19385);const s=(0,r.default)(o.default,"Promise");const u=(0,r.default)(o.default,"Set");const c=(0,r.default)(o.default,"WeakMap");var l=n(89572),f=n(65114),d="[object Map]",_="[object Promise]",h="[object Set]",p="[object WeakMap]",b="[object DataView]",v=(0,f.default)(i),g=(0,f.default)(a.default),m=(0,f.default)(s),y=(0,f.default)(u),w=(0,f.default)(c),x=l.default;(i&&x(new i(new ArrayBuffer(1)))!=b||a.default&&x(new a.default)!=d||s&&x(s.resolve())!=_||u&&x(new u)!=h||c&&x(new c)!=p)&&(x=function(e){var t=(0,l.default)(e),n="[object Object]"==t?e.constructor:void 0,r=n?(0,f.default)(n):"";if(r)switch(r){case v:return b;case g:return d;case m:return _;case y:return h;case w:return p}return t})
;const j=x},85146:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(82433),o=Object.create;const i=function(){function e(){}return function(t){if(!(0,r.default)(t))return{};if(o)return o(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();var a=n(10964),s=n(5196);const u=function(e){return"function"!=typeof e.constructor||(0,s.default)(e)?{}:i((0,a.default)(e))}},99313:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=/^(?:0|[1-9]\d*)$/;const o=function(e,t){var n=typeof e;return!!(t=t??9007199254740991)&&("number"==n||"symbol"!=n&&r.test(e))&&e>-1&&e%1==0&&e<t}},61833:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(54523),o=n(49634),i=n(99313),a=n(82433);const s=function(e,t,n){if(!(0,a.default)(n))return!1;var s=typeof t;return!!("number"==s?(0,o.default)(n)&&(0,i.default)(t,n.length):"string"==s&&t in n)&&(0,r.default)(n[t],e)}},61070:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(56052),o=n(98111),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;const s=function(e,t){if((0,r.default)(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!(0,o.default)(e))||(a.test(e)||!i.test(e)||null!=t&&e in Object(t))}},5196:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=Object.prototype;const o=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||r)}},92350:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(97889),o="object"==typeof exports&&exports&&!exports.nodeType&&exports,i=o&&"object"==typeof module&&module&&!module.nodeType&&module,a=i&&i.exports===o&&r.default.process;const s=function(){try{var e=i&&i.require&&i.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}()},45635:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e,t){return function(n){return e(t(n))}}},99615:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(97889),o="object"==typeof self&&self&&self.Object===Object&&self;const i=r.default||o||Function("return this")()},13383:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(98111);const o=function(e){if("string"==typeof e||(0,r.default)(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},65114:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=Function.prototype.toString;const o=function(e){if(null!=e){try{return r.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},90054:(e,t,n)=>{"use strict";n.d(t,{default:()=>K});var r=n(87593);const o=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e};var i=n(61572),a=n(52949),s=n(77251);const u=function(e,t){return e&&(0,a.default)(t,(0,s.default)(t),e)};var c=n(2960);const l=function(e,t){return e&&(0,a.default)(t,(0,c.default)(t),e)};var f=n(14054),d=n(32126),_=n(21578);const h=function(e,t){return(0,a.default)(e,(0,_.default)(e),t)};var p=n(18573),b=n(10964),v=n(69043);const g=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)(0,p.default)(t,(0,_.default)(e)),e=(0,b.default)(e);return t}:v.default;const m=function(e,t){return(0,a.default)(e,g(e),t)}
;var y=n(38366),w=n(96909);const x=function(e){return(0,w.default)(e,c.default,g)};var j=n(81296),E=Object.prototype.hasOwnProperty;const S=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&E.call(e,"index")&&(n.index=e.index,n.input=e.input),n};var O=n(22605);const z=function(e,t){var n=t?(0,O.default)(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)};var A=/\w*$/;const P=function(e){var t=new e.constructor(e.source,A.exec(e));return t.lastIndex=e.lastIndex,t};var k=n(66711),R=k.default?k.default.prototype:void 0,L=R?R.valueOf:void 0;const C=function(e){return L?Object(L.call(e)):{}};var N=n(11523);const B=function(e,t,n){var r=e.constructor;switch(t){case"[object ArrayBuffer]":return(0,O.default)(e);case"[object Boolean]":case"[object Date]":return new r(+e);case"[object DataView]":return z(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return(0,N.default)(e,n);case"[object Map]":case"[object Set]":return new r;case"[object Number]":case"[object String]":return new r(e);case"[object RegExp]":return P(e);case"[object Symbol]":return C(e)}};var T=n(85146),M=n(56052),I=n(32437),q=n(43888),F=n(82433),$=n(13795);const D=function(e){return(0,$.default)(e)&&"[object Set]"==(0,j.default)(e)};var V=n(5467),U=n(92350),W=U.default&&U.default.isSet;const H=W?(0,V.default)(W):D;var Z="[object Arguments]",Q="[object Function]",G="[object Object]",J={};J[Z]=J["[object Array]"]=J["[object ArrayBuffer]"]=J["[object DataView]"]=J["[object Boolean]"]=J["[object Date]"]=J["[object Float32Array]"]=J["[object Float64Array]"]=J["[object Int8Array]"]=J["[object Int16Array]"]=J["[object Int32Array]"]=J["[object Map]"]=J["[object Number]"]=J[G]=J["[object RegExp]"]=J["[object Set]"]=J["[object String]"]=J["[object Symbol]"]=J["[object Uint8Array]"]=J["[object Uint8ClampedArray]"]=J["[object Uint16Array]"]=J["[object Uint32Array]"]=!0,J["[object Error]"]=J[Q]=J["[object WeakMap]"]=!1;const Y=function e(t,n,a,_,p,b){var v,g=1&n,w=2&n,E=4&n;if(a&&(v=p?a(t,_,p,b):a(t)),void 0!==v)return v;if(!(0,F.default)(t))return t;var O=(0,M.default)(t);if(O){if(v=S(t),!g)return(0,d.default)(t,v)}else{var z=(0,j.default)(t),A=z==Q||"[object GeneratorFunction]"==z;if((0,I.default)(t))return(0,f.default)(t,g);if(z==G||z==Z||A&&!p){if(v=w||A?{}:(0,T.default)(t),!g)return w?m(t,l(v,t)):h(t,u(v,t))}else{if(!J[z])return p?t:{};v=B(t,z,g)}}b||(b=new r.default);var P=b.get(t);if(P)return P;b.set(t,v),H(t)?t.forEach((function(r){v.add(e(r,n,a,r,t,b))})):(0,q.default)(t)&&t.forEach((function(r,o){v.set(o,e(r,n,a,o,t,b))}));var k=E?w?x:y.default:w?c.default:s.default,R=O?void 0:k(t);return o(R||t,(function(r,o){R&&(r=t[o=r]),(0,i.default)(v,o,e(r,n,a,o,t,b))})),v};const K=function(e){return Y(e,5)}},90484:(e,t,n)=>{"use strict";n.d(t,{default:()=>c});var r=n(82433),o=n(99615);const i=function(){
return o.default.Date.now()};var a=n(78677),s=Math.max,u=Math.min;const c=function(e,t,n){var o,c,l,f,d,_,h=0,p=!1,b=!1,v=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function g(t){var n=o,r=c;return o=c=void 0,h=t,f=e.apply(r,n)}function m(e){var n=e-_;return void 0===_||n>=t||n<0||b&&e-h>=l}function y(){var e=i();if(m(e))return w(e);d=setTimeout(y,function(e){var n=t-(e-_);return b?u(n,l-(e-h)):n}(e))}function w(e){return d=void 0,v&&o?g(e):(o=c=void 0,f)}function x(){var e=i(),n=m(e);if(o=arguments,c=this,_=e,n){if(void 0===d)return function(e){return h=e,d=setTimeout(y,t),p?g(e):f}(_);if(b)return clearTimeout(d),d=setTimeout(y,t),g(_)}return void 0===d&&(d=setTimeout(y,t)),f}return t=(0,a.default)(t)||0,(0,r.default)(n)&&(p=!!n.leading,l=(b="maxWait"in n)?s((0,a.default)(n.maxWait)||0,t):l,v="trailing"in n?!!n.trailing:v),x.cancel=function(){void 0!==d&&clearTimeout(d),h=0,o=_=c=d=void 0},x.flush=function(){return void 0===d?f:w(i())},x}},54523:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e,t){return e===t||e!=e&&t!=t}},54029:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(49084);const o=function(e,t,n){var o=null==e?void 0:(0,r.default)(e,t);return void 0===o?n:o}},76402:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return e}},54404:(e,t,n)=>{"use strict";n.d(t,{default:()=>c});var r=n(89572),o=n(13795);const i=function(e){return(0,o.default)(e)&&"[object Arguments]"==(0,r.default)(e)};var a=Object.prototype,s=a.hasOwnProperty,u=a.propertyIsEnumerable;const c=i(function(){return arguments}())?i:function(e){return(0,o.default)(e)&&s.call(e,"callee")&&!u.call(e,"callee")}},56052:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=Array.isArray},49634:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(88987),o=n(65743);const i=function(e){return null!=e&&(0,o.default)(e.length)&&!(0,r.default)(e)}},83350:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(89572),o=n(13795);const i=function(e){return!0===e||!1===e||(0,o.default)(e)&&"[object Boolean]"==(0,r.default)(e)}},32437:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(99615);const o=function(){return!1};var i="object"==typeof exports&&exports&&!exports.nodeType&&exports,a=i&&"object"==typeof module&&module&&!module.nodeType&&module,s=a&&a.exports===i?r.default.Buffer:void 0;const u=(s?s.isBuffer:void 0)||o},15943:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(89815),o=n(81296),i=n(54404),a=n(56052),s=n(49634),u=n(32437),c=n(5196),l=n(9125),f=Object.prototype.hasOwnProperty;const d=function(e){if(null==e)return!0;if((0,s.default)(e)&&((0,a.default)(e)||"string"==typeof e||"function"==typeof e.splice||(0,u.default)(e)||(0,l.default)(e)||(0,i.default)(e)))return!e.length;var t=(0,o.default)(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if((0,c.default)(e))return!(0,r.default)(e).length;for(var n in e)if(f.call(e,n))return!1;return!0}},50279:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(12189);const o=function(e,t){return(0,r.default)(e,t)}},88987:(e,t,n)=>{
"use strict";n.d(t,{default:()=>i});var r=n(89572),o=n(82433);const i=function(e){if(!(0,o.default)(e))return!1;var t=(0,r.default)(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},65743:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},43888:(e,t,n)=>{"use strict";n.d(t,{default:()=>c});var r=n(81296),o=n(13795);const i=function(e){return(0,o.default)(e)&&"[object Map]"==(0,r.default)(e)};var a=n(5467),s=n(92350),u=s.default&&s.default.isMap;const c=u?(0,a.default)(u):i},63193:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return null==e}},69708:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(89572),o=n(13795);const i=function(e){return"number"==typeof e||(0,o.default)(e)&&"[object Number]"==(0,r.default)(e)}},82433:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},13795:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){return null!=e&&"object"==typeof e}},83873:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(89572),o=n(56052),i=n(13795);const a=function(e){return"string"==typeof e||!(0,o.default)(e)&&(0,i.default)(e)&&"[object String]"==(0,r.default)(e)}},98111:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(89572),o=n(13795);const i=function(e){return"symbol"==typeof e||(0,o.default)(e)&&"[object Symbol]"==(0,r.default)(e)}},9125:(e,t,n)=>{"use strict";n.d(t,{default:()=>f});var r=n(89572),o=n(65743),i=n(13795),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1;const s=function(e){return(0,i.default)(e)&&(0,o.default)(e.length)&&!!a[(0,r.default)(e)]};var u=n(5467),c=n(92350),l=c.default&&c.default.isTypedArray;const f=l?(0,u.default)(l):s},77251:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(60545),o=n(89815),i=n(49634);const a=function(e){return(0,i.default)(e)?(0,r.default)(e):(0,o.default)(e)}},2960:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(60545),o=n(82433),i=n(5196);const a=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t};var s=Object.prototype.hasOwnProperty;const u=function(e){if(!(0,o.default)(e))return a(e);var t=(0,i.default)(e),n=[];for(var r in e)("constructor"!=r||!t&&s.call(e,r))&&n.push(r);return n};var c=n(49634);const l=function(e){return(0,c.default)(e)?(0,r.default)(e,!0):u(e)}},82593:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(e){
var t=null==e?0:e.length;return t?e[t-1]:void 0}},59332:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(75440);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=e.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(o.Cache||r.default),n}o.Cache=r.default;const i=o},16738:(e,t,n)=>{"use strict";n.d(t,{default:()=>H});var r=n(87593),o=n(857),i=n(54523);const a=function(e,t,n){(void 0!==n&&!(0,i.default)(e[t],n)||void 0===n&&!(t in e))&&(0,o.default)(e,t,n)};var s=n(76507),u=n(14054),c=n(11523),l=n(32126),f=n(85146),d=n(54404),_=n(56052),h=n(49634),p=n(13795);const b=function(e){return(0,p.default)(e)&&(0,h.default)(e)};var v=n(32437),g=n(88987),m=n(82433),y=n(89572),w=n(10964),x=Function.prototype,j=Object.prototype,E=x.toString,S=j.hasOwnProperty,O=E.call(Object);const z=function(e){if(!(0,p.default)(e)||"[object Object]"!=(0,y.default)(e))return!1;var t=(0,w.default)(e);if(null===t)return!0;var n=S.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&E.call(n)==O};var A=n(9125);const P=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]};var k=n(52949),R=n(2960);const L=function(e){return(0,k.default)(e,(0,R.default)(e))};const C=function(e,t,n,r,o,i,s){var h=P(e,n),p=P(t,n),y=s.get(p);if(y)a(e,n,y);else{var w=i?i(h,p,n+"",e,t,s):void 0,x=void 0===w;if(x){var j=(0,_.default)(p),E=!j&&(0,v.default)(p),S=!j&&!E&&(0,A.default)(p);w=p,j||E||S?(0,_.default)(h)?w=h:b(h)?w=(0,l.default)(h):E?(x=!1,w=(0,u.default)(p,!0)):S?(x=!1,w=(0,c.default)(p,!0)):w=[]:z(p)||(0,d.default)(p)?(w=h,(0,d.default)(h)?w=L(h):(0,m.default)(h)&&!(0,g.default)(h)||(w=(0,f.default)(p))):x=!1}x&&(s.set(p,w),o(w,p,r,i,s),s.delete(p)),a(e,n,w)}};const N=function e(t,n,o,i,u){t!==n&&(0,s.default)(n,(function(s,c){if(u||(u=new r.default),(0,m.default)(s))C(t,n,c,o,e,i,u);else{var l=i?i(P(t,c),s,c+"",t,n,u):void 0;void 0===l&&(l=s),a(t,c,l)}}),R.default)};var B=n(76402);const T=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)};var M=Math.max;const I=function(e,t,n){return t=M(void 0===t?e.length-1:t,0),function(){for(var r=arguments,o=-1,i=M(r.length-t,0),a=Array(i);++o<i;)a[o]=r[t+o];o=-1;for(var s=Array(t+1);++o<t;)s[o]=r[o];return s[t]=n(a),T(e,this,s)}};const q=function(e){return function(){return e}};var F=n(55136);const $=F.default?function(e,t){return(0,F.default)(e,"toString",{configurable:!0,enumerable:!1,value:q(t),writable:!0})}:B.default;var D=Date.now;const V=function(e){var t=0,n=0;return function(){var r=D(),o=16-(r-n);if(n=r,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}($);const U=function(e,t){return V(I(e,t,B.default),e+"")};var W=n(61833);const H=function(e){return U((function(t,n){
var r=-1,o=n.length,i=o>1?n[o-1]:void 0,a=o>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(o--,i):void 0,a&&(0,W.default)(n[0],n[1],a)&&(i=o<3?void 0:i,o=1),t=Object(t);++r<o;){var s=n[r];s&&e(t,s,r,i)}return t}))}((function(e,t,n){N(e,t,n)}))},81251:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(56882);const o=function(e,t){var n;if("function"!=typeof t)throw new TypeError("Expected a function");return e=(0,r.default)(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=void 0),n}};const i=function(e){return o(2,e)}},39852:(e,t,n)=>{"use strict";n.d(t,{default:()=>T});const r=function(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n};var o=n(76507),i=n(77251);const a=function(e,t){return e&&(0,o.default)(e,t,i.default)};var s=n(49634);const u=function(e,t){return function(n,r){if(null==n)return n;if(!(0,s.default)(n))return e(n,r);for(var o=n.length,i=t?o:-1,a=Object(n);(t?i--:++i<o)&&!1!==r(a[i],i,a););return n}}(a);var c=n(87593),l=n(12189);const f=function(e,t,n,r){var o=n.length,i=o,a=!r;if(null==e)return!i;for(e=Object(e);o--;){var s=n[o];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++o<i;){var u=(s=n[o])[0],f=e[u],d=s[1];if(a&&s[2]){if(void 0===f&&!(u in e))return!1}else{var _=new c.default;if(r)var h=r(f,d,u,e,t,_);if(!(void 0===h?(0,l.default)(d,f,3,r,_):h))return!1}}return!0};var d=n(82433);const _=function(e){return e==e&&!(0,d.default)(e)};const h=function(e){for(var t=(0,i.default)(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,_(o)]}return t};const p=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}};const b=function(e){var t=h(e);return 1==t.length&&t[0][2]?p(t[0][0],t[0][1]):function(n){return n===e||f(n,e,t)}};var v=n(54029);const g=function(e,t){return null!=e&&t in Object(e)};var m=n(31434),y=n(54404),w=n(56052),x=n(99313),j=n(65743),E=n(13383);const S=function(e,t,n){for(var r=-1,o=(t=(0,m.default)(t,e)).length,i=!1;++r<o;){var a=(0,E.default)(t[r]);if(!(i=null!=e&&n(e,a)))break;e=e[a]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&(0,j.default)(o)&&(0,x.default)(a,o)&&((0,w.default)(e)||(0,y.default)(e))};const O=function(e,t){return null!=e&&S(e,t,g)};var z=n(61070);const A=function(e,t){return(0,z.default)(e)&&_(t)?p((0,E.default)(e),t):function(n){var r=(0,v.default)(n,e);return void 0===r&&r===t?O(n,e):(0,l.default)(t,r,3)}};var P=n(76402);const k=function(e){return function(t){return null==t?void 0:t[e]}};var R=n(49084);const L=function(e){return function(t){return(0,R.default)(t,e)}};const C=function(e){return(0,z.default)(e)?k((0,E.default)(e)):L(e)};const N=function(e){return"function"==typeof e?e:null==e?P.default:"object"==typeof e?(0,w.default)(e)?A(e[0],e[1]):b(e):C(e)};const B=function(e,t,n,r,o){return o(e,(function(e,o,i){n=r?(r=!1,e):t(n,e,o,i)})),n};const T=function(e,t,n){var o=(0,w.default)(e)?r:B,i=arguments.length<3;return o(e,N(t,4),n,i,u)}},47339:(e,t,n)=>{"use strict";n.d(t,{default:()=>c});var r=n(61572),o=n(31434),i=n(99313),a=n(82433),s=n(13383)
;const u=function(e,t,n,u){if(!(0,a.default)(e))return e;for(var c=-1,l=(t=(0,o.default)(t,e)).length,f=l-1,d=e;null!=d&&++c<l;){var _=(0,s.default)(t[c]),h=n;if("__proto__"===_||"constructor"===_||"prototype"===_)return e;if(c!=f){var p=d[_];void 0===(h=u?u(p,_,d):void 0)&&(h=(0,a.default)(p)?p:(0,i.default)(t[c+1])?[]:{})}(0,r.default)(d,_,h),d=d[_]}return e};const c=function(e,t,n){return null==e?e:u(e,t,n)}},69043:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});const r=function(){return[]}},20057:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var r=n(90484),o=n(82433);const i=function(e,t,n){var i=!0,a=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return(0,o.default)(n)&&(i="leading"in n?!!n.leading:i,a="trailing"in n?!!n.trailing:a),(0,r.default)(e,t,{leading:i,maxWait:t,trailing:a})}},56882:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(78677),o=1/0;const i=function(e){return e?(e=(0,r.default)(e))===o||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0};const a=function(e){var t=i(e),n=t%1;return t==t?n?t-n:t:0}},78677:(e,t,n)=>{"use strict";n.d(t,{default:()=>_});var r=/\s/;const o=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t};var i=/^\s+/;const a=function(e){return e?e.slice(0,o(e)+1).replace(i,""):e};var s=n(82433),u=n(98111),c=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,f=/^0o[0-7]+$/i,d=parseInt;const _=function(e){if("number"==typeof e)return e;if((0,u.default)(e))return NaN;if((0,s.default)(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=(0,s.default)(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var n=l.test(e);return n||f.test(e)?d(e.slice(2),n?2:8):c.test(e)?NaN:+e}},81960:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(31434),o=n(82593),i=n(49084),a=n(38459);const s=function(e,t){return t.length<2?e:(0,i.default)(e,(0,a.default)(t,0,-1))};var u=n(13383);const c=function(e,t){return t=(0,r.default)(t,e),null==(e=s(e,t))||delete e[(0,u.default)((0,o.default)(t))]};const l=function(e,t){return null==e||c(e,t)}}}]);