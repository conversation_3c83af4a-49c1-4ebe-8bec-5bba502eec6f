'use client';

import { createContext, type ReactNode, useContext } from 'react';

interface PublicModeContextType {
  isPublic: boolean;
}

const PublicModeContext = createContext<PublicModeContextType | undefined>(undefined);

interface Props {
  children: ReactNode;
  token: string | undefined;
}

export const PublicModeProvider: React.FC<Props> = ({ children, token }) => {
  const isPublic = !token;

  return <PublicModeContext.Provider value={{ isPublic }}>{children}</PublicModeContext.Provider>;
};

export const usePublicMode = () => {
  const context = useContext(PublicModeContext);
  if (context === undefined) {
    throw new Error('usePublicMode must be used within a PublicModeProvider');
  }
  return context;
};
