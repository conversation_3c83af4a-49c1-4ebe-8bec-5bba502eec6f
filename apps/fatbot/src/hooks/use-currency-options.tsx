import { useTranslations } from 'next-intl';
import type { ReactNode } from 'react';

import { Link } from '@/assets/link';
import { type Chain as ChainT, useGetAllChains } from '@/lib/api';
import { ChainIcon } from '@/module/multichain/chain-icon';
import { getChainConfig } from '@/module/multichain/get-chain-config';

export type ChainTExtended = ChainT | 'ALL';

export interface CurrencyOption {
  icon?: ReactNode;
  value: ChainTExtended;
  label: string;
}

export const useCurrencyOptions = (showAllChainOption?: boolean): CurrencyOption[] => {
  const t = useTranslations('bot-dex-selector');
  const { data } = useGetAllChains();

  return [
    ...(showAllChainOption
      ? [
          {
            icon: <Link className="size-2" />,
            value: 'ALL' as const,
            label: t('all'),
          },
        ]
      : []),
    ...(data?.enabledChains ?? []).map((chain) => ({
      icon: <ChainIcon chain={chain} className="size-2" />,
      label: getChainConfig(chain).chainName,
      value: chain,
    })),
  ];
};
