import type { Meta, StoryObj } from '@storybook/nextjs';

import { QuickSellButton } from '@/components/form/quick-sell-button';

const meta: Meta<typeof QuickSellButton> = {
  title: 'Quick Sell Button',
  component: QuickSellButton,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof QuickSellButton>;

export const Idle: Story = {
  args: {
    txState: 'OPENED',
  },
};

export const Pending: Story = {
  args: {
    txState: 'PENDING_CLOSED',
  },
};

export const Success: Story = {
  args: {
    txState: 'CONFIRMED_CLOSED',
  },
};

export const Error: Story = {
  args: {
    txState: 'OPEN_FAILED',
  },
};

export const WithCustomClass: Story = {
  args: {
    txState: 'OPENED',
    className: 'bg-primary text-white',
  },
};

export const WithClickHandler: Story = {
  args: {
    txState: 'OPENED',
  },
  render: (args) => (
    <QuickSellButton {...args} onClick={() => { alert('Button clicked!'); }} />
  ),
};
