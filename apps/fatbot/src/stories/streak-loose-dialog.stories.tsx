import React from 'react';

import { StreakLooseDialog } from '@/module/league/streak/components/streak-loose-dialog';
import type { StreakConfig } from '@/module/league/streak/types';

const meta = {
  title: 'Streak/StreakLooseDialog',
  component: StreakLooseDialog,
};

export default meta;

const streakConfig: StreakConfig[] = [
  {
    label: 'Tu',
    isChecked: true,
    isHighlighted: false,
    variant: 'default',
  },
  {
    label: 'We',
    isChecked: true,
    isHighlighted: false,
    variant: 'default',
  },
  {
    label: 'Th',
    isChecked: false,
    isHighlighted: false,
    variant: 'default',
  },
  {
    label: 'Fr',
    isChecked: false,
    isHighlighted: false,
    variant: 'default',
  },
  {
    label: 'Sa',
    isChecked: false,
    isHighlighted: false,
    variant: 'default',
  },
  {
    label: 'Su',
    isChecked: false,
    isHighlighted: false,
    variant: 'default',
  },
];

export const Default = {
  name: 'StreakLooseDialog',
  render: () => (
    <StreakLooseDialog
      currentMultiplier="1.5"
      daysInStreak={12}
      open={true}
      streakConfig={streakConfig}
      streakExpiresAt="2025-05-29T00:00:00Z"
      onOpenChange={() => {}}
    />
  ),
};
