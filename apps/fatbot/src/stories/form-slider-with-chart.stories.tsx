import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { useTranslations } from 'next-intl';
import type { FC } from 'react';
import { useForm } from 'react-hook-form';

import type { SliderChartData } from '@/components/charts/slider-chart/utils';
import { Form } from '@/components/form/form';
import { FormSliderWithChart, type Props } from '@/components/slider/form-slider-with-chart';
import { Button } from '@/components/ui/button';

const FormSliderPrimitive: FC<
  Props & {
    defaultValue: number[];
  }
> = ({ defaultValue, ...props }) => {
  const t = useTranslations();
  const form = useForm({ defaultValues: { [props.name]: defaultValue } });

  const onSubmit = (formData: Record<string, number[]>) => {
    alert(
      `submitted: ${JSON.stringify({
        [props.name]: {
          min: formData[props.name]?.[0],
          max: formData[props.name]?.[1],
        },
      })}`,
    );
  };

  return (
    <div className="mt-4 max-w-(--breakpoint-sm)">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormSliderWithChart {...props} />

          <Button className="w-full" type="submit">
            {t('common.submit')}
          </Button>
        </form>
      </Form>
    </div>
  );
};

const exampleData: SliderChartData = [
  { valueFrom: 0, valueTo: 1, count: 1 },
  { valueFrom: 1, valueTo: 2, count: 2 },
  { valueFrom: 2, valueTo: 3, count: 3 },
  { valueFrom: 3, valueTo: 4, count: 4 },
  { valueFrom: 4, valueTo: 5, count: 5 },
  { valueFrom: 5, valueTo: 6, count: 10 },
  { valueFrom: 6, valueTo: 7, count: 11 },
  { valueFrom: 7, valueTo: 8, count: 12 },
  { valueFrom: 8, valueTo: 9, count: 15 },
  { valueFrom: 9, valueTo: 10, count: 14 },
  { valueFrom: 10, valueTo: 11, count: 12 },
  { valueFrom: 11, valueTo: 12, count: 11 },
  { valueFrom: 12, valueTo: 13, count: 10 },
  { valueFrom: 13, valueTo: 14, count: 8 },
  { valueFrom: 14, valueTo: 15, count: 7 },
  { valueFrom: 15, valueTo: 16, count: 6 },
  { valueFrom: 16, valueTo: 17, count: 5 },
  { valueFrom: 17, valueTo: 18, count: 4 },
  { valueFrom: 18, valueTo: 19, count: 3 },
  { valueFrom: 19, valueTo: 20, count: 2 },
];

const meta: Meta<typeof FormSliderPrimitive> = {
  title: 'FormSliderWithChart',
  args: {
    name: 'slider',
    displayData: exampleData,
    type: 'range',
    step: 1,
    defaultValue: [0, Infinity],
    minInputPlaceholder: 'Min',
    maxInputPlaceholder: 'Max',
  },
};

export default meta;

export const Default: FormSliderWithChartStory = {
  render: (props) => <FormSliderPrimitive {...props} />,
};

type FormSliderWithChartStory = StoryObj<typeof FormSliderPrimitive>;

export const WithInputPrefix: FormSliderWithChartStory = {
  args: {
    inputPrefix: '$',
  },
  render: (props) => <FormSliderPrimitive {...props} />,
};

export const WithInfiniteValue: FormSliderWithChartStory = {
  args: {
    includeInfinityValue: true,
    inputPrefix: '$',
    defaultValue: [0, Infinity],
  },
  render: (props) => <FormSliderPrimitive {...props} />,
};

export const CustomDefaultValue: FormSliderWithChartStory = {
  args: {
    defaultValue: [5, 15],
  },
  render: (props) => <FormSliderPrimitive {...props} />,
};

export const SingleValueDisplayedAsRange: FormSliderWithChartStory = {
  args: {
    type: 'singleValue',
    displayAs: 'range',
    defaultValue: [19],
  },
  render: (props) => <FormSliderPrimitive {...props} />,
};

export const SingleValueDisplayedAsPoint: FormSliderWithChartStory = {
  args: {
    type: 'singleValue',
    displayAs: 'singleValue',
    defaultValue: [12],
  },
  render: (props) => <FormSliderPrimitive {...props} />,
};
