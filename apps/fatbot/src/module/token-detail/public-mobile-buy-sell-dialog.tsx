import {
  AlertDialogDescription,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@radix-ui/react-alert-dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { BackButton } from '@/components/back-button';
import { AlertDialog, AlertDialogContent } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import type { Chain, DexDetail, TokenAuditDto } from '@/lib/api';
import { getTradeButtonVariant } from '@/module/token-detail/buy-warning/utils';
import { PublicManualTradingForm } from '@/module/token-detail/manual-trading/public-manual-trading-form';
import {
  makeAuditVariantFromRiskFactor,
  makeAuditVariantIcon,
} from '@/module/token-detail/token-audit/utils';

interface Props {
  chain: Chain;
  tokenInfo: Required<DexDetail>;
  tokenAudit: TokenAuditDto | undefined;
  isTokenContractVerified: boolean;
}

export const PublicMobileBuySellDialog = ({
  tokenInfo,
  chain,
  tokenAudit,
  isTokenContractVerified,
}: Props) => {
  const t = useTranslations('token-detail');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<'buy' | 'sell'>('buy');
  const auditVariant = makeAuditVariantFromRiskFactor(
    tokenAudit?.result?.riskFactor
  );

  return (
    <div className="fixed inset-x-3 bottom-0 bg-fading-gradient py-6 sm:inset-x-5 lg:hidden">
      <div className="flex justify-center gap-3 ">
        <AlertDialog
          open={isDialogOpen}
          onOpenChange={(value) => { setIsDialogOpen(value); }}
        >
          <VisuallyHidden>
            <AlertDialogTitle />
            <AlertDialogDescription />
          </VisuallyHidden>

          {isDialogOpen ? <BackButton
              className="fixed left-3 top-3 z-9999"
              onClick={() => { setIsDialogOpen(false); }}
            /> : null}
          <AlertDialogTrigger asChild={true}>
            <Button
              className="flex-1 px-2"
              variant={getTradeButtonVariant(auditVariant)}
              onClick={() => {
                setSelectedTab('buy');
                setIsDialogOpen(true);
              }}
            >
              {makeAuditVariantIcon(auditVariant, 'size-3')}
              {t('buy')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogTrigger asChild={true}>
            <Button
              className="flex-1 bg-surface-background px-2"
              variant="outline"
              onClick={() => {
                setSelectedTab('sell');
                setIsDialogOpen(true);
              }}
            >
              {t('sell')}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent className="pt-10" hideClose>
            <PublicManualTradingForm
              chain={chain}
              defaultTab={selectedTab}
              isTokenContractVerified={isTokenContractVerified}
              tokenAudit={tokenAudit}
              tokenInfo={tokenInfo}
            />
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};
