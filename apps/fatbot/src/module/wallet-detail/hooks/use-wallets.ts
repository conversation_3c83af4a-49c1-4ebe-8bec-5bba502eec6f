import { useMemo } from 'react';

import { REFETCH_INTERVAL_30_SECONDS } from '@/api/constants';
import { useGetAllChains, useGetAllUserWalletsV3 } from '@/lib/api';

export const useWallets = () => {
  const { data: allChains } = useGetAllChains();

  const { data: wallets = [], isLoading } = useGetAllUserWalletsV3(
    { useSelectedChains: false },
    {
      query: {
        refetchInterval: REFETCH_INTERVAL_30_SECONDS,
        refetchIntervalInBackground: false,
        refetchOnWindowFocus: true,
      },
    },
  );

  const walletCounts: Record<string, number> = useMemo(() => {
    const walletsByChain = Object.groupBy(wallets, (wallet) => wallet.chain);
    const walletCounts = Object.fromEntries(
      Object.entries(walletsByChain).map(([chain, value]) => [chain, value.length]),
    );

    return {
      ...walletCounts,
      ALL: wallets.length,
    };
  }, [wallets]);

  return {
    walletCounts,
    wallets,
    enabledChains: allChains?.enabledChains ?? [],
    isLoading,
  };
};
