import deepmerge from 'deepmerge';
import { getRequestConfig } from 'next-intl/server';

import { getSelectedLocale } from '@/module/i18n/lib/get-selected-locale';

export default getRequestConfig(async () => {
  const locale = await getSelectedLocale();

  const defaultMessages: object = (await import(`../../../messages/en.json`)).default;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
  const userMessages: object = (await import(`../../../messages/${locale}.json`)).default;

  return {
    locale,
    messages: deepmerge(defaultMessages, userMessages),
  };
});
