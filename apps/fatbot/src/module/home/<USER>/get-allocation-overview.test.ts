import { getAllocationOverview } from './get-allocation-overview';

describe('getAllocationOverview', () => {
  it('calculates correct percentages and values for normal input', () => {
    const result = getAllocationOverview({
      manualTradingValueUsd: '200',
      botTradingValueUsd: '800',
    });
    expect(result.manualTradingValue).toBe('200');
    expect(result.botTradingValue).toBe('800');
    expect(Number(result.manualTradingPercent)).toBeCloseTo(0.2);
    expect(Number(result.botTradingPercent)).toBeCloseTo(0.8);
  });

  it('returns 0% for both when both values are zero', () => {
    const result = getAllocationOverview({
      manualTradingValueUsd: '0',
      botTradingValueUsd: '0',
    });
    expect(result.manualTradingPercent).toBe('0');
    expect(result.botTradingPercent).toBe('0');
  });

  it('handles missing values as zero', () => {
    const result = getAllocationOverview({});
    expect(result.manualTradingValue).toBe('0');
    expect(result.botTradingValue).toBe('0');
    expect(result.manualTradingPercent).toBe('0');
    expect(result.botTradingPercent).toBe('0');
  });

  it('handles only manual trading value', () => {
    const result = getAllocationOverview({ manualTradingValueUsd: '1000' });
    expect(result.manualTradingValue).toBe('1000');
    expect(result.botTradingValue).toBe('0');
    expect(result.manualTradingPercent).toBe('1');
    expect(result.botTradingPercent).toBe('0');
  });

  it('handles only bot trading value', () => {
    const result = getAllocationOverview({ botTradingValueUsd: '500' });
    expect(result.manualTradingValue).toBe('0');
    expect(result.botTradingValue).toBe('500');
    expect(result.manualTradingPercent).toBe('0');
    expect(result.botTradingPercent).toBe('1');
  });

  it('handles string and number inputs', () => {
    const result = getAllocationOverview({
      manualTradingValueUsd: 300,
      botTradingValueUsd: '700',
    });
    expect(result.manualTradingValue).toBe('300');
    expect(result.botTradingValue).toBe('700');
    expect(Number(result.manualTradingPercent)).toBeCloseTo(0.3);
    expect(Number(result.botTradingPercent)).toBeCloseTo(0.7);
  });
});
