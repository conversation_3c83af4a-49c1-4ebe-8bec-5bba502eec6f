import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import { getTranslations } from 'next-intl/server';

import portfolioChartEmpty from '@/assets/images/portfolio-chart-empty.svg';
import { ProfitCard } from '@/components/profit-card';
import { formatUsd } from '@/lib/formatters/format-usd';

export const PublicPortfolio = async () => {
  const t = await getTranslations();

  return (
    <div className="flex flex-col gap-2 @container/portfolio">
      <div className="grid">
        <h3 className="text-display-l font-bold" data-testid="total-portfolio-value">
          {formatUsd(0)}
        </h3>
        <p className="text-md font-medium capitalize text-text-secondary" data-testid="total-portfolio-label">
          {t('home-page.total-portfolio-value')}
        </p>
      </div>
      <Image
        alt="Portfolio"
        className="h-auto w-full"
        draggable={false}
        priority={true}
        src={portfolioChartEmpty as StaticImageData}
      />
      <div className="grid grid-cols-[1fr] gap-1 @[360px]/portfolio:grid-cols-2">
        <ProfitCard testId="total-profit-value" title={t('home-page.total-profit')} trend="0" value="0" />
        <ProfitCard testId="total-1d-change-value" title={t('home-page.total-1d-change')} trend="0" value="0" />
      </div>
    </div>
  );
};
