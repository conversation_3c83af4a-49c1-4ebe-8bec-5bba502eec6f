import { getTranslations } from 'next-intl/server';

import { OnboardingAnimationStep3 } from '@/components/animations/onboarding-step-3';
import { DataListHeader } from '@/components/data-list/data-list-header';
import { Card } from '@/components/ui/card';

export const MyTopBots: React.FC = async () => {
  const t = await getTranslations();

  return (
    <div className="grid gap-2">
      <DataListHeader title={t('home-page.my-top-bots')} />
      <Card className="gap-3 shadow-primary @container/my-top-bots" variant="primary">
        <div className="grid place-items-center gap-0.25">
          <OnboardingAnimationStep3 className="flex max-w-[220px] @[300px]/my-top-bots:max-w-[260px] @[400px]/my-top-bots:max-w-[320px]" />

          <h3 className="-mt-1.5 text-display-s font-extrabold uppercase leading-[55%]! @[300px]/my-top-bots:text-[41px] @[400px]/my-top-bots:text-display-l">
            {t('home-page.coming-soon')}
          </h3>
        </div>
        <p className="mx-auto max-w-[350px] text-center text-sm font-medium capitalize leading-14 text-text-secondary">
          {t('home-page.sniping-2-0-coming')}
        </p>
      </Card>
    </div>
  );
};
