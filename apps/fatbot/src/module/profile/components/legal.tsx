'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { AppVersion } from './app-version';
import { SectionContainer } from './section-container';
import { SectionTitle } from './section-title';
import { SectionsContainer } from './sections-container';

import { logout } from '@/api';
import { File05 } from '@/assets/file-05';
import { File06 } from '@/assets/file-06';
import { LogOut01 } from '@/assets/log-out-01';
import { Menu } from '@/components/menu/menu';
import { MenuItem } from '@/components/menu/menu-item';
import { ROUTES } from '@/constants/routes';

export const Legal: React.FC = () => {
  const t = useTranslations('profile');
  const queryClient = useQueryClient();
  const router = useRouter();

  const handleSignOut = async (event: React.MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault();
    await logout();
    queryClient.clear();
    router.refresh();
  };

  return (
    <SectionsContainer className="gap-4 md:gap-2">
      <SectionContainer>
        <SectionTitle title={t('legal')} />
        <Menu>
          <MenuItem bordered href={ROUTES.TERMS_AND_CONDITIONS} icon={<File06 />} label={t('terms-and-conditions')} />
          <MenuItem bordered href={ROUTES.PRIVACY_POLICY} icon={<File05 />} label={t('privacy-policy')} />
          <MenuItem icon={<LogOut01 />} label={t('log-out')} onClick={handleSignOut} />
        </Menu>
      </SectionContainer>
      <AppVersion />
    </SectionsContainer>
  );
};
