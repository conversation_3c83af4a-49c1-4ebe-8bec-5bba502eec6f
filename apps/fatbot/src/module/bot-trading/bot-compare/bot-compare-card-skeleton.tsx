import { DonutChartSkeleton } from '@/components/charts/donut-chart/donut-chart-skeleton';
import { Skeleton } from '@/components/skeleton';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';

export const BotCompareCardSkeleton = () => (
  <div className="mt-5 space-y-5 rounded-lg bg-transparent p-0 md:mt-2 md:bg-surface-area md:p-3 @container/BACS">
    {/* Portfolio skeleton */}
    <div className="flex flex-col gap-5">
      <div className="flex w-full justify-between h-76">
        <Skeleton className="h-3 w-16" />
        <BotAvatar
          avatarFileId="1150985b-0ee3-49ac-8548-c574853d143e"
          className="size-auto min-h-[240px] lg:h-[300px] opacity-5"
          priority={true}
        />
        <Skeleton className="h-4 w-16 " />
      </div>
      <div>
        <Skeleton className="h-30 w-50 mb-4" />
        <div className="mt-1.5 space-y-3">
          <Skeleton className="w-full h-[200px]" />
          <Skeleton className="h-5 w-full rounded-full" />
        </div>
      </div>
    </div>
    {/* BotActivity skeleton */}
    <div className="grid gap-3 md:gap-1">
      <div className="flex h-7 flex-col">
        <Skeleton className="h-4 w-32 mb-1" />
        <Skeleton className="h-3 w-20" />
      </div>

      <DonutChartSkeleton size={285} />

      <div className="flex flex-col gap-y-3 md:gap-y-2">
        <Skeleton className="h-22 w-full" />
        <div className="grid grid-cols-1 gap-3 @[280px]/BACS:grid-cols-2 md:gap-2">
          <Skeleton className="h-22 w-full" />
          <Skeleton className="h-22 w-full" />
        </div>
        <Skeleton className="h-22 w-full" />
      </div>
    </div>

    {/* BotDetailTrades skeleton */}
    <div className="hidden @[340px]/BACS:grid">
      <Skeleton className="h-6 w-32 mb-2" />
      <Skeleton className="h-7.5 w-full rounded-xs shadow-primary mb-2" />
      <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
    </div>

    {/* BotMyTransactions skeleton */}
    <div className="hidden lg:block">
      <Skeleton className="h-6 w-32 mb-2" />
      <Skeleton className="h-7.5 w-full rounded-xs shadow-primary mb-2" />
      <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
    </div>
  </div>
);
