import { match } from 'ts-pattern';

import { BotStatusActiveLottie } from '@/components/animations/bot-status-active';
import { BotStatusDeactivatedLottie } from '@/components/animations/bot-status-deactived';
import { BotStatusLimitedLottie } from '@/components/animations/bot-status-limited';
import { BotStatusNoBalanceLottie } from '@/components/animations/bot-status-no-balance';
import { BotStatus } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Props {
  status: BotStatus;
}

export const BotStatusAnimation: React.FC<Props> = ({ status }) => {
  const animationBgColor = match(status)
    .with(BotStatus.HAS_PURCHASED, () => 'bg-event-success-background')
    .with(BotStatus.NO_PURCHASE, () => 'bg-event-success-background')
    .with(BotStatus.INSUFFICIENT_BALANCE, () => 'bg-event-error-background')
    .with(BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS, () => 'bg-event-warning-background')
    .with(BotStatus.BUY_DAILY_LIMIT_REACHED, () => 'bg-event-warning-background')
    .with(BotStatus.DEACTIVATED, () => 'bg-surface-third-layer/80')
    .exhaustive();

  const animation = match(status)
    .with(BotStatus.HAS_PURCHASED, () => (
      <BotStatusActiveLottie className="size-5 shrink-0" />
    ))
    .with(BotStatus.NO_PURCHASE, () => (
      <BotStatusActiveLottie className="size-5 shrink-0" />
    ))
    .with(BotStatus.BUY_DAILY_LIMIT_REACHED, BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS, () => (
      <BotStatusLimitedLottie className="size-5 shrink-0" />
    ))
    .with(BotStatus.DEACTIVATED, () => (
      <BotStatusDeactivatedLottie className="size-5 shrink-0" />
    ))
    .with(BotStatus.INSUFFICIENT_BALANCE, () => (
      <BotStatusNoBalanceLottie className="size-5 shrink-0" />
    ))
    .exhaustive();

  return (
    <div
      className={cn(
        animationBgColor,
        'flex items-center justify-center size-7 rounded-xxs p-1 shrink-0'
      )}
    >
      {animation}
    </div>
  );
};
