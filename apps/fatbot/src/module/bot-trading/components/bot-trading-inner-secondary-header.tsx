import { dehydrate, HydrationBoundary } from '@tanstack/react-query';

import { AppNavigation } from '@/components/app-header/app-navigation';
import { ProfileLink } from '@/components/profile-link';
import { ROUTES } from '@/constants/routes';
import { getGetAllChainsQueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { ChainSelector } from '@/module/bot-trading/components/chain-selector';
import { BackLink } from '@/module/routing/back-link';

export const BotTradingInnerSecondaryHeader = async () => {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(getGetAllChainsQueryOptions());

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="grid grid-cols-2 items-center px-0 py-2 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
        <BackLink defaultUrl={ROUTES.BOT_TRADING.ROOT} />
        <AppNavigation />
        <div className="flex items-center justify-end gap-3">
          <ChainSelector />
          <ProfileLink />
        </div>
      </div>
    </HydrationBoundary>
  );
};
