import { motion } from 'motion/react';
import { useTranslations } from 'next-intl';
import { type Dispatch, type FC, type SetStateAction, useEffect } from 'react';

import { CheckmarkIcon } from '@/assets/checkmark-icon';
import { TradeValidationAnimation } from '@/components/animations/trade-validation';
import { Button } from '@/components/ui/button';
import { CircularProgress } from '@/components/ui/circular-progress';
import { formatRichText } from '@/lib/formatters/format-rich-text';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';

import { BotAddressCard } from './bot-address-card';
import { LaunchBotStatus, type LaunchBotStepProps, LaunchStep } from './utils';

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

type Props = LaunchBotStepProps & {
  status: LaunchBotStatus;
  setStatus: Dispatch<SetStateAction<LaunchBotStatus>>;
  botWalletAddress: string;
  botName: string;
  botAvatarFileId: string;
};

const SMALL_DELAY = 3_000;
const BIG_DELAY = 5_000;

const useLaunchProgressLabels = () => {
  const t = useTranslations('bot-trading.bot-launch-dialog.launch-bot-step.progress-labels');

  return [
    {
      status: LaunchBotStatus['CONTRACT_CHECKING'],
      label: t('contract-checking'),
    },
  ];
};

export const LaunchBotStep: FC<Props> = ({
  status,
  setStatus,
  setStep,
  botAvatarFileId,
  botName,
  botWalletAddress,
}) => {
  const t = useTranslations('bot-trading.bot-launch-dialog');
  const progressLabels = useLaunchProgressLabels();

  useEffect(() => {
    const updateState = async () => {
      await delay(SMALL_DELAY);
      setStatus(LaunchBotStatus.CONTRACT_CHECKING);

      await delay(SMALL_DELAY);
      setStatus(LaunchBotStatus.PENDING);

      await delay(BIG_DELAY);
      setStatus(LaunchBotStatus.SUCCESS);
    };

    void updateState();
  }, [setStatus]);

  return (
    <div>
      {status === LaunchBotStatus.SUCCESS ? (
        <div>
          <div className="-my-2 text-center text-display-s font-bold text-text-invert sm:-my-0">
            {t.rich('launch-bot-step.success-message', formatRichText)}
          </div>
          <div className="relative">
            <CircularProgress className="invisible -my-2 sm:-my-0" progress={0} />

            <BotAvatar
              avatarFileId={botAvatarFileId}
              className="absolute inset-x-0 top-1/2 mt-1 size-full max-h-none -translate-y-1/2 p-5"
            />
          </div>
          <div className="flex flex-col gap-2">
            <BotAddressCard address={botWalletAddress} label={t('launch-bot-step.copy-address')} />

            <Button
              variant="invert"
              onClick={() => {
                setStep(LaunchStep.DEPOSIT);
              }}
            >
              {t('continue')}
            </Button>
          </div>
        </div>
      ) : null}

      {[LaunchBotStatus.INITIATED, LaunchBotStatus.CONTRACT_CHECKING, LaunchBotStatus.PENDING].includes(status) ? (
        <div>
          <div className="text-center text-display-s font-bold text-text-secondary">
            {t.rich('launch-bot-step.message', {
              name: botName,
              ...formatRichText,
            })}
          </div>

          <div className="relative overflow-hidden">
            <CircularProgress className="-my-1 sm:-my-0" infinite />

            <TradeValidationAnimation className="absolute inset-x-0 top-1/2 size-full -translate-y-1/2 p-8" />
          </div>
          <div className="flex flex-col items-center gap-1">
            {progressLabels.map(({ status: labelStatus, label }) => (
              <motion.span
                key={label}
                animate={{ opacity: status >= labelStatus ? 1 : 0 }}
                className="flex items-center gap-1 text-center text-md text-text-secondary"
                initial={{ opacity: 0 }}
              >
                {label}
                <div className="flex size-3 items-center justify-center rounded-full border-2 border-border-subtle">
                  <motion.div animate={{ opacity: status > labelStatus ? 1 : 0 }} initial={{ opacity: 0 }}>
                    <CheckmarkIcon className="size-1 text-surface-active" />
                  </motion.div>
                </div>
              </motion.span>
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
};
