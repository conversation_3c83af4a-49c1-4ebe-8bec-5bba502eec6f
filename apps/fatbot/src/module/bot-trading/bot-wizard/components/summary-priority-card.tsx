import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { EXTERNAL_LINKS } from '@/constants/external-links';
import { cn } from '@/lib/utils';

export const SummaryPriorityCard = ({ className }: { className?: string }) => {
  const t = useTranslations('bot-trading.create-bot.bot-summary.priority');

  return (
    <Card className={cn('flex flex-col gap-2 shadow-primary', className)} variant="primary">
      <div className="flex items-end justify-between">
        <h4 className="text-display-xs font-bold">{t('title')}</h4>
        <p className="text-sm font-semibold text-text-elevated">{t('read-more')}</p>
      </div>
      <p className="text-sm font-semibold text-text-elevated">{t('description')}</p>
      <Button asChild className="h-7 px-0" variant="outline">
        <Link href={EXTERNAL_LINKS.FATBOT_WEB} rel="noopener noreferrer" target="_blank">
          {t('cta')}
        </Link>
      </Button>
    </Card>
  );
};
