import { useTranslations } from 'next-intl';
import { memo } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { REQUIRED_STEP_SUMMARY_INDEX } from '@/module/bot-trading/bot-wizard/constants';
import { useBotWizardStep } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-step';
import { getWizardTotalSteps } from '@/module/bot-trading/bot-wizard/step-content';
export interface Props extends React.ComponentProps<typeof Button> {
  label?: string;
}

export const SkipButton = memo((props: Props) => {
  const t = useTranslations('bot-trading.create-bot');
  const { currentStep, setStep, next } = useBotWizardStep();

  const isRequiredStepSummary = currentStep === REQUIRED_STEP_SUMMARY_INDEX;

  const onSkipClick = () => {
    // this step is after required steps, so we need to show summary step
    if (isRequiredStepSummary) {
      setStep(getWizardTotalSteps());
      return;
    }

    void next();
  };

  const buttonText = t('skip');

  return (
    <Button
      variant="outline"
      onClick={onSkipClick}
      {...props}
      className={cn('w-full sm:w-fit', {
        'w-1/2': isRequiredStepSummary,
      })}
    >
      {buttonText}
    </Button>
  );
});

SkipButton.displayName = 'SkipButton';
