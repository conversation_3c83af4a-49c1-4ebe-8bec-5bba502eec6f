'use client';

import { parseAsString, useQueryStates } from 'nuqs';
import { median } from 'ramda';

import { STRING_INFINITY } from '@/components/slider/utilities';
import { useGetBotSettingsStatistics } from '@/lib/api';
import { getDefaultBotAvatarId } from '@/module/bot-trading/bots-config';

const getDefaultUpperBound = (data: number[], percentage = false): string => {
  const medianValue = median(data);

  // this ceils up the value if percentage values are enabled
  return (percentage ? medianValue : Math.ceil(medianValue)).toString();
};

const getDefaultLowerBound = (data: number[], requiredField = false) => {
  const firstValue = data[0];

  // if required field and first value is 0, return the second value
  const defaultValue = requiredField && firstValue === 0 ? data[1] : firstValue;

  return defaultValue?.toString() ?? '';
};

const useDefaultValues = () => {
  const { data } = useGetBotSettingsStatistics();

  const defaultDataset = {
    buyFrequency: parseAsString.withDefault(
      getDefaultUpperBound((data?.buyFrequency ?? []).map(({ from }) => Number(from))),
    ),
    profitTargetFraction: parseAsString.withDefault(
      getDefaultUpperBound(
        (data?.profitTarget ?? []).map(({ from }) => Number(from)),
        true,
      ),
    ),
    stopLossFraction: parseAsString.withDefault(
      getDefaultUpperBound(
        (data?.stopLoss ?? []).map(({ from }) => Number(from)),
        true,
      ),
    ),
    marketCapFromUsd: parseAsString.withDefault(
      getDefaultLowerBound((data?.marketCapUsd ?? []).map(({ from }) => Number(from))),
    ),
    marketCapToUsd: parseAsString.withDefault(STRING_INFINITY),
    liquidityFromUsd: parseAsString.withDefault(
      getDefaultLowerBound((data?.liquidityUsd ?? []).map(({ from }) => Number(from))),
    ),
    liquidityToUsd: parseAsString.withDefault(STRING_INFINITY),
    dailyVolumeFromUsd: parseAsString.withDefault(
      getDefaultLowerBound((data?.dailyVolumeUsd ?? []).map(({ from }) => Number(from))),
    ),
    dailyVolumeToUsd: parseAsString.withDefault(STRING_INFINITY),
    numberOfHoldersFrom: parseAsString.withDefault(
      getDefaultLowerBound((data?.numberOfHolders ?? []).map(({ from }) => Number(from))),
    ),
    numberOfHoldersTo: parseAsString.withDefault(STRING_INFINITY),
  };

  return {
    avatarFileId: parseAsString.withDefault(getDefaultBotAvatarId()),
    buyFrequency: defaultDataset['buyFrequency'],
    name: parseAsString.withDefault(''),
    profitTargetFraction: defaultDataset['profitTargetFraction'],
    stopLossFraction: defaultDataset['stopLossFraction'],
    tradeAmount: parseAsString.withDefault(''),
    // optional fields
    marketCapFromUsd: defaultDataset['marketCapFromUsd'],
    marketCapToUsd: defaultDataset['marketCapToUsd'],
    liquidityFromUsd: defaultDataset['liquidityFromUsd'],
    liquidityToUsd: defaultDataset['liquidityToUsd'],
    dailyVolumeFromUsd: defaultDataset['dailyVolumeFromUsd'],
    dailyVolumeToUsd: defaultDataset['dailyVolumeToUsd'],
    numberOfHoldersFrom: defaultDataset['numberOfHoldersFrom'],
    numberOfHoldersTo: defaultDataset['numberOfHoldersTo'],

    buyVolume: parseAsString.withDefault('0.5'),
    sellVolume: parseAsString.withDefault('0.5'),
    buyTransactionFraction: parseAsString.withDefault('0.9'),
    sellTransactionFraction: parseAsString.withDefault('0.1'),
  };
};

export const useBotWizardFormData = () => {
  const defaultValues = useDefaultValues();

  return useQueryStates(defaultValues);
};

export type BotWizardFormData = ReturnType<typeof useBotWizardFormData>[0];
export type BotSummaryData = Partial<BotWizardFormData>;
