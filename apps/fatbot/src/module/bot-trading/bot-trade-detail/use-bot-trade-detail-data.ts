import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import type { StaticImageData } from 'next/image';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { REFETCH_INTERVAL_5_SECONDS } from '@/api/constants';
import { BuyIndicator, SellIndicator } from '@/assets/chart-icons';
import { toast } from '@/components/toast';
import {
  type BotTradeTransaction,
  getGetBotMarketPositionQueryOptions,
  useForceSellMarketPositions,
  useGetBotMarketPosition,
} from '@/lib/api';

export const useBotTradeDetailData = () => {
  const t = useTranslations();
  const { botId, tradeId } = useParams<{ botId: string; tradeId: string }>();
  const queryClient = useQueryClient();

  const { data: botTradeData, isLoading } = useGetBotMarketPosition(botId, tradeId, {
    query: {
      retry: 1,
      refetchInterval: REFETCH_INTERVAL_5_SECONDS,
      placeholderData: keepPreviousData,
      select: (data) => {
        const trades = [data.buyTradeTransaction, data.sellTradeTransaction].filter(Boolean) as BotTradeTransaction[];

        return {
          trades,
          botTrade: data,
          tradePoints: trades.map((trade) => ({
            time: new Date(trade.txCreatedAt).getTime(),
            minSize: 40,
            color: {
              border: 'transparent',
              background: 'transparent',
            },
            txType: trade.txType,
            imageUrl:
              trade.txType === 'BUY' ? (BuyIndicator as StaticImageData).src : (SellIndicator as StaticImageData).src,
            text: trade.txType === 'BUY' ? t('common.buy') : t('common.sell'),
          })),
        };
      },
    },
  });

  const { mutate: forceSell, isPending: isSellPending } = useForceSellMarketPositions({
    mutation: {
      onSuccess: async () => {
        toast.success(t('bot-trading.bot-trades.sell-initiated'));
        await queryClient.invalidateQueries(getGetBotMarketPositionQueryOptions(botId, tradeId));
      },
      onError: () => toast.error(t('bot-trading.bot-trades.failed-to-sell-token')),
    },
  });

  return {
    botTradeData,
    isLoading,
    forceSell,
    isSellPending,
  };
};
