import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';

import { Skeleton } from '@/components/skeleton';
import { cn } from '@/lib/utils';

const hiddenRewardCardSkeletonVariants = cva('shadow-primary', {
  variants: {
    variant: {
      tiny: 'w-[208px] h-[338px]',
      large: 'w-[258px] h-[437px] p-2 rounded-xxl',
    },
  },
  defaultVariants: {
    variant: 'tiny',
  },
});

interface HiddenRewardCardSkeletonProps extends VariantProps<typeof hiddenRewardCardSkeletonVariants> {
  className?: string;
}

export const HiddenRewardCardSkeleton = ({ className, variant }: HiddenRewardCardSkeletonProps) => (
  <Skeleton className={cn(hiddenRewardCardSkeletonVariants({ variant }), className)} />
);
