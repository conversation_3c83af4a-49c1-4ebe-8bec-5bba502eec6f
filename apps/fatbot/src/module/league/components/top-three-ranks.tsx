import type { StaticImport } from 'next/dist/shared/lib/get-img-props';
import Image from 'next/image';

import { LeaderboardBase, LeaderboardBase1, LeaderboardBase2, LeaderboardBase3 } from '@/assets/images';
import { FireworksAnimation } from '@/components/animations/fireworks-animation';
import type { SearchUserLeaderboardResult } from '@/lib/api';
import { BotCharacter, BOTS_CONFIG } from '@/module/bot-trading/bots-config';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';

interface Props {
  showFireworks: boolean;
  top3Ranks: SearchUserLeaderboardResult[];
}

export const TopThreeRanks: React.FC<Props> = ({ showFireworks, top3Ranks }) => {
  const firstRank = top3Ranks[0];
  const secondRank = top3Ranks[1];
  const thirdRank = top3Ranks[2];

  return (
    <div className="flex flex-col gap-1">
      <div className="relative mx-auto flex size-fit pt-4">
        <div className="absolute inset-0 flex items-end justify-end">
          <div className="flex flex-1 flex-col items-center justify-end">
            {showFireworks ? (
              <FireworksAnimation className="absolute bottom-[140px] left-[10px] z-0 size-[150px] md:bottom-[130px]  md:left-[-66px] md:size-[400px]" />
            ) : null}

            <BotAvatar
              avatarFileId={BOTS_CONFIG[BotCharacter.FatMask].avatarFileId}
              className="relative z-10 mb-[-1%] size-[160px] md:size-[260px]"
              variant="medal"
            />

            <Image alt="LeaderboardBase2" className="h-fit w-full" src={LeaderboardBase2 as StaticImport} />
          </div>
          <div className="flex flex-1 flex-col items-center justify-end">
            <BotAvatar
              avatarFileId={BOTS_CONFIG[BotCharacter.FatChef].avatarFileId}
              className="z-10 mb-[-1%] size-[160px] md:size-[260px]"
              variant="medal"
            />

            <Image alt="LeaderboardBase1" className="h-fit w-full" src={LeaderboardBase1 as StaticImport} />
          </div>
          <div className="flex flex-1 flex-col items-center justify-end">
            {showFireworks ? (
              <FireworksAnimation className="absolute bottom-[160px] right-[20px] z-0 size-[150px] sm:bottom-[140px] sm:size-[250px] md:bottom-15 md:right-[-35px] md:size-[400px] lg:bottom-[180px] xl:bottom-[220px]" />
            ) : null}

            <BotAvatar
              avatarFileId={BOTS_CONFIG[BotCharacter.Fatanders].avatarFileId}
              className="z-10 mb-[-1%] size-[160px] md:size-[260px]"
              variant="medal"
            />

            <Image alt="LeaderboardBase3" className="h-fit w-full" src={LeaderboardBase3 as StaticImport} />
          </div>
        </div>
        <Image
          alt={'LeaderboardBase'}
          className="z-10 h-fit w-full max-w-[670px]"
          src={LeaderboardBase as StaticImport}
        />
      </div>
      <div className="grid h-3 w-full grid-cols-3">
        <h2 className="text-center text-md font-bold capitalize">{secondRank?.email}</h2>
        <h2 className="text-center text-md font-bold capitalize">{firstRank?.email}</h2>
        <h2 className="text-center text-md font-bold capitalize">{thirdRank?.email}</h2>
      </div>
    </div>
  );
};
