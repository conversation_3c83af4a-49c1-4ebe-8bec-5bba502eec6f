'use client';

import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import SuccessfullyClaimed from '@/assets/images/successfully-claimed.svg';
import { Confetti, LeagueComingSoonBg } from '@/assets/league';
import { Badge } from '@/components/ui/badge';

export const LeagueComingSoonContent = () => {
  const t = useTranslations('league.coming-soon');

  return (
    <>
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <Image
          alt=""
          className="absolute -top-28 left-0 size-full object-cover xl:object-contain"
          fill
          priority
          src={LeagueComingSoonBg}
        />
      </div>

      <div className="relative max-w-90 mx-auto bg-surface-brand-1 mt-7 p-3 rounded-xxl overflow-visible">
        <div className="absolute top-4 left-0 right-0 h-40 w-full pointer-events-none">
          <Image alt="" className="object-cover" fill src={Confetti} />
        </div>

        <div className="flex flex-col items-center gap-3 text-center">
          <div className="flex items-center mt-2 justify-center">
            <Image
              alt=""
              className="object-contain"
              height={243}
              src={(SuccessfullyClaimed as StaticImageData).src}
              width={162}
            />
          </div>

          <div className="flex flex-col items-center gap-2">
            <Badge size="medium" variant="success-darker">
              {t('badge')}
            </Badge>

            <h1 className="text-display-l font-bold text-text-invert">{t('title')}</h1>
          </div>

          <p className="text-md font-semibold text-surface-grey max-w-54">{t('subtitle')}</p>
        </div>
      </div>
    </>
  );
};
