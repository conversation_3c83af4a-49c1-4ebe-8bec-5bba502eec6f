'use client';
import { useTranslations } from 'next-intl';

import { AlertTriangle } from '@/assets';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useRemainingTime } from '@/hooks/use-remaining-time';
import { isLessThanXHours } from '@/lib/dates/get-hours-difference-from-date';
import { isNumber } from '@/lib/type-checks';
import { cn } from '@/lib/utils';
import type { StreakConfig } from '@/module/league/streak/types';

const INTERVAL_TIME = 60_000;
const HOURS_TO_ADD = 0;

interface Props {
  config: StreakConfig[];
  daysToNextStreak?: number;
  title?: string;
  className?: string;
  radioGroupClassName?: string;
  onDayClick?: (day: StreakConfig) => void;
  streakExpiresAt?: string;
}

export const DayStreak: React.FC<Props> = ({
  config,
  className,
  daysToNextStreak,
  title,
  onDayClick,
  radioGroupClassName,
  streakExpiresAt,
}) => {
  const t = useTranslations('streak');
  const timeToStreakExpire = useRemainingTime(streakExpiresAt ?? '', INTERVAL_TIME, HOURS_TO_ADD);
  const isLessThanTwoHours = isLessThanXHours(streakExpiresAt, 2);

  return (
    <div className={cn('w-full space-y-2', className)}>
      <div className="flex items-center justify-between">
        {title ? (
          <div className="flex items-center">
            <span className="text-display-xs font-bold text-text-primary">{title}</span>
          </div>
        ) : null}
        {isLessThanTwoHours ? (
          <Badge className="gap-0.5 h-4" size="medium" variant="warning">
            <AlertTriangle className="size-2" />
            {t('at-risk')}
          </Badge>
        ) : null}
      </div>

      <RadioGroup className={cn('flex flex-wrap justify-between pointer-events-none', radioGroupClassName)}>
        {config.map((item) => (
          <div key={item.label} className="flex">
            <RadioGroupItem
              checked={item.isChecked}
              highlighted={item.isHighlighted}
              label={item.label}
              labelPosition="top"
              size="md"
              value={item.label}
              variant={item.variant ?? 'default'}
              onClick={() => onDayClick?.(item)}
            />
          </div>
        ))}
      </RadioGroup>

      {isNumber(daysToNextStreak) && daysToNextStreak > 0 ? (
        <p className="text-sm font-medium text-text-elevated">
          {t('just-x-more-days-to-unlock-next-reward', {
            count: daysToNextStreak,
          })}
        </p>
      ) : null}

      {isLessThanTwoHours && timeToStreakExpire.formatted ? (
        <p className="text-sm font-semibold text-event-warning-content mt-2">
          {t('trade-now-to-save-your-multiplier', {
            timeToStreakExpire: timeToStreakExpire.formatted,
          })}
        </p>
      ) : null}
    </div>
  );
};
