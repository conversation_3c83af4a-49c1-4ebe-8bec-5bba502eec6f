'use client';

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface StreakDialogState {
  lastDayStreakDialogShown: string | null; // YYYY-MM-DD format
  lastStreakLooseDialogShown: string | null; // YYYY-MM-DD format
  lastLostStreakDialogShown: string | null; // YYYY-MM-DD format
  setLastDayStreakDialogShown: (date: string) => void;
  setLastStreakLooseDialogShown: (date: string) => void;
  setLastLostStreakDialogShown: (date: string) => void;
  resetDialogStates: () => void;
}

export const useStreakDialogStore = create<StreakDialogState>()(
  persist(
    (set) => ({
      lastDayStreakDialogShown: null,
      lastStreakLooseDialogShown: null,
      lastLostStreakDialogShown: null,
      setLastDayStreakDialogShown: (date) => {
        set({ lastDayStreakDialogShown: date });
      },
      setLastStreakLooseDialogShown: (date) => {
        set({ lastStreakLooseDialogShown: date });
      },
      setLastLostStreakDialogShown: (date) => {
        set({ lastLostStreakDialogShown: date });
      },
      resetDialogStates: () => {
        set({
          lastDayStreakDialogShown: null,
          lastStreakLooseDialogShown: null,
          lastLostStreakDialogShown: null,
        });
      },
    }),
    {
      name: 'streak-dialogs-store',
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
