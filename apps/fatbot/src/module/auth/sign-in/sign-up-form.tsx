'use client';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Mail } from '@/assets';
import { AlertErrorMessage } from '@/components/alert-message';
import { ErrorMessage } from '@/components/error-message';
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/components/form/form';
import { Input } from '@/components/form/input';
import { PasswordInput } from '@/components/form/password-input';
import { LoadingButton } from '@/components/ui/loading-button';
import { SIGNUP_CONSTRAINTS } from '@/constants';
import { ROUTES } from '@/constants/routes';
import { useSignUpForm } from '@/module/auth/sign-in/use-signup-form';

export const SignUpForm = () => {
  const t = useTranslations('sign-up');

  const { form, onSubmit } = useSignUpForm();

  const { errors, isSubmitSuccessful } = form.formState;
  const { minimumPasswordLength } = SIGNUP_CONSTRAINTS;

  return isSubmitSuccessful && Object.keys(errors).length === 0 ? (
    <p className="text-center text-base text-text-secondary">
      {t.rich('confirmation', {
        email: form.getValues('email'),
        b: (chunks) => <b>{chunks}</b>,
      })}
    </p>
  ) : (
    <Form {...form}>
      <form className="flex w-full flex-col gap-3" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input autoComplete="username" icon={<Mail />} placeholder={t('email-placeholder')} {...field} />
                </FormControl>
                <AlertErrorMessage className="-mt-3" inputName="email" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput autoComplete="new-password" placeholder={t('password-placeholder')} {...field} />
                </FormControl>
                <AlertErrorMessage className="-mt-3" inputName="password" />
                <FormDescription>
                  {t('password-description', {
                    min: minimumPasswordLength,
                  })}
                </FormDescription>
              </FormItem>
            )}
          />

          {errors.root?.serverError ? (
            <ErrorMessage className="mt-3">{errors.root.serverError.message}</ErrorMessage>
          ) : null}
        </div>

        <LoadingButton className="w-full" isLoading={form.formState.isSubmitting} type="submit">
          {t('cta')}
        </LoadingButton>

        <p className="text-balance text-center text-sm font-medium text-text-secondary">
          {t.rich('disclaimer', {
            a: (chunks) => (
              <Link className="font-semibold text-text-active" href={ROUTES.TERMS_AND_CONDITIONS}>
                {chunks}
              </Link>
            ),
            b: (chunks) => (
              <Link className="font-semibold text-text-active" href={ROUTES.PRIVACY_POLICY}>
                {chunks}
              </Link>
            ),
          })}
        </p>
      </form>
    </Form>
  );
};
