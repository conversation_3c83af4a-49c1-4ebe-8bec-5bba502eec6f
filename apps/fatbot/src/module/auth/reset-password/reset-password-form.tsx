'use client';
import { useTranslations } from 'next-intl';

import { Mail } from '@/assets';
import { AlertErrorMessage } from '@/components/alert-message';
import { Form, FormControl, FormField, FormItem } from '@/components/form/form';
import { Input } from '@/components/form/input';
import { Alert, AlertTitle } from '@/components/ui/alert';
import { LoadingButton } from '@/components/ui/loading-button';

import { useResetPasswordForm } from './use-reset-password-form';

export const ForgotPasswordForm = () => {
  const t = useTranslations('reset-password');

  const { form, onSubmit } = useResetPasswordForm();

  const { isSubmitting, isSubmitSuccessful, isSubmitted } = form.formState;

  return (
    <Form {...form}>
      <form className="flex w-full flex-col gap-3" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input autoComplete="username" icon={<Mail />} placeholder={t('email-placeholder')} {...field} />
                </FormControl>
                <AlertErrorMessage className="-mt-3" inputName="email" />
              </FormItem>
            )}
          />

          {isSubmitSuccessful && isSubmitted ? (
            <Alert>
              <AlertTitle>{t('reset-password-email-sent')}</AlertTitle>
            </Alert>
          ) : null}
        </div>

        <LoadingButton className="w-full" isLoading={isSubmitting}>
          {t('cta')}
        </LoadingButton>
      </form>
    </Form>
  );
};
