'use client';

import { useQueryState } from 'nuqs';

import { Tabs, TabsContent } from '@/components/ui/tabs';

import { BeginSetupButton } from './begin-setup-button';
import { ConnectAuthenticator } from './connect-authenticator';
import { EnrollMfaTotpForm } from './enroll-mfa-totp-form';
import { MFAProvider } from './mfa-context';
import { ReauthenticateForm } from './reauthenticate-form';

export const SetupMFAFlow = () => {
  const [selectedTab, setSelectedTab] = useQueryState('step', {
    defaultValue: 'setup-mfa',
  });

  return (
    <MFAProvider>
      <Tabs
        className="flex w-full flex-col gap-5"
        defaultValue={selectedTab}
        value={selectedTab}
        onValueChange={(value) => void setSelectedTab(value)}
      >
        <TabsContent value="setup-mfa">
          <BeginSetupButton />
        </TabsContent>

        <TabsContent value="reauthenticate">
          <ReauthenticateForm />
        </TabsContent>

        <TabsContent value="connect-authenticator">
          <ConnectAuthenticator />
        </TabsContent>

        <TabsContent value="verify-setup">
          <EnrollMfaTotpForm />
        </TabsContent>
      </Tabs>
    </MFAProvider>
  );
};
