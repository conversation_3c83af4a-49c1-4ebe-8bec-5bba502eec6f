import { useTranslations } from 'next-intl';

import { ChevronRight } from '@/assets';
import { Coins } from '@/assets/coins';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { TokenPosition } from '@/module/assets/types';
import { WalletTokenItem } from '@/module/wallet-withdraw/wallet-token-item';

interface Props {
  onClick: () => void;
  token?: TokenPosition;
}

export const WithdrawTokenInput = ({ onClick, token }: Props) => {
  const t = useTranslations('wallets');
  return (
    <button className="w-full" type="button" onClick={onClick}>
      <Card
        className={cn(
          'flex-row items-center gap-2 shadow-primary',
          token && 'p-1'
        )}
        variant="primary"
      >
        {token ? (
          <WalletTokenItem
            amount={token.tokenNativeAmount}
            chain={token.tokenChain}
            imageUrl={token.tokenImageUrl}
            name={token.tokenName}
            price={token.pricePerTokenInUsd}
            radius="rounded-xs"
          />
        ) : (
          <div className="flex flex-1 items-center gap-2">
            <Coins className="size-3 text-border-secondary" />
            <p className="text-md font-semibold">{t('choose-token')}</p>
          </div>
        )}
        <ChevronRight className="size-3 text-border-secondary" />
      </Card>
    </button>
  );
};
