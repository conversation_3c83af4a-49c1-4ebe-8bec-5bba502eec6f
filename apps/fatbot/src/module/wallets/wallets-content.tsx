'use client';

import { useTranslations } from 'next-intl';
import { parseAsStringLiteral, useQueryState } from 'nuqs';
import { Fragment } from 'react';

import { Skeleton } from '@/components/skeleton';
import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import { UserChainTabs } from '@/components/user-chain-tabs';
import type { ChainTExtended } from '@/hooks/use-currency-options';
import { sortWallets } from '@/lib/sort/sort-wallets';
import { CreateWalletButton } from '@/module/deposit-to-wallet/create-wallet-button';
import { useFilteredWallets } from '@/module/wallet-detail/hooks/use-filtered-wallets';
import { useWallets } from '@/module/wallet-detail/hooks/use-wallets';
import { WalletCard } from '@/module/wallets/wallet-card';

import { SELECTED_TAB_QUERY, useTabsLiteral } from './use-tabs-literal';

export const WalletsContent = () => {
  const t = useTranslations('wallets');
  const options = useTabsLiteral();

  const [activeTab, setActiveTab] = useQueryState<ChainTExtended>(
    SELECTED_TAB_QUERY,
    parseAsStringLiteral(options).withDefault('ALL').withOptions({
      history: 'replace',
    }),
  );

  const { wallets, isLoading } = useWallets();
  const { filteredWallets } = useFilteredWallets(wallets, activeTab);

  return (
    <Card className="mx-auto max-w-2xl gap-3">
      <div className="flex flex-col gap-2">
        <Display className="flex items-center justify-between" size="L" weight="bold">
          {t('my-wallets')}
          <div className="flex items-center justify-between">
            <CreateWalletButton chain={activeTab} wallets={wallets} />
          </div>
        </Display>
      </div>

      <UserChainTabs value={activeTab} onChange={(value: ChainTExtended) => void setActiveTab(value)} />

      {(() => {
        switch (true) {
          case isLoading:
            return (
              <div className="flex flex-col gap-y-2">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton key={index} className="size-full h-32" />
                ))}
              </div>
            );
          case filteredWallets.length <= 0:
            return <p className="text-sm font-medium text-text-secondary">{t('no-wallets')}</p>;
          default:
            return (
              <div className="flex flex-col gap-2">
                {sortWallets(filteredWallets).map((wallet) => (
                  <Fragment key={wallet.walletId}>
                    { }
                    <WalletCard {...wallet} />
                  </Fragment>
                ))}
              </div>
            );
        }
      })()}
    </Card>
  );
};
