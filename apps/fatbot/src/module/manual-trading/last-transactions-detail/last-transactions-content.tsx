'use client';

import { useTranslations } from 'next-intl';

import { DetailListHeader } from '@/components/data-list/detail-list-header';
import { DetailListContainer } from '@/components/detail-list-container';
import { Card } from '@/components/ui/card';
import type { GetAllUserWalletsResult } from '@/lib/api';
import { WalletSelectSearch } from '@/module/manual-trading/components/wallet-select-search';

import { LastTransactionsVirtualList } from './last-transactions-virtual-list';

interface Props {
  wallets: GetAllUserWalletsResult[];
}

export const LastTransactionsContent: React.FC<Props> = ({ wallets }) => {
  const t = useTranslations();

  return (
    <Card
      className="mx-auto flex max-w-(--breakpoint-lg) flex-col gap-y-2"
      variant="area"
    >
      <DetailListHeader
        title={t('common.transactions')}
        titleClassName="md:ml-0 text-display-m"
      >
        <WalletSelectSearch wallets={wallets} />
      </DetailListHeader>

      <DetailListContainer>
        <LastTransactionsVirtualList />
      </DetailListContainer>
    </Card>
  );
};
