import { SearchIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';

interface Props {
  onClick: () => void;
}

export const SearchTokenButton: React.FC<Props> = ({ onClick }) => {
  const t = useTranslations('manual-trading');

  return (
    <Button
      className="z-10 mt-[95px] flex h-7 w-full justify-start gap-1 rounded-full bg-surface-third-layer px-2 text-md font-semibold capitalize text-text-secondary shadow-primary transition-colors hover:bg-surface-subtle sm:mt-0"
      size="sm"
      variant="secondary"
      onClick={onClick}
    >
      <SearchIcon className="size-3 text-secondary-front" />
      {t('paste-token-address')}
    </Button>
  );
};
