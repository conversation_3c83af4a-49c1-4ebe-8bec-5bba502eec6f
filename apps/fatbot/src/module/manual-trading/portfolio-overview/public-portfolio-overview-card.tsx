import { getTranslations } from 'next-intl/server';

import { ProfitCard } from '@/components/profit-card';
import { Card } from '@/components/ui/card';
import { formatUsd } from '@/lib/formatters/format-usd';

export const PublicPortfolioOverviewCard: React.FC = async () => {
  const t = await getTranslations();

  return (
    <Card
      className="gap-3 bg-surface-area px-0 @container/POC sm:p-3"
      variant="primary"
    >
      <div className="grid grid-cols-1">
        <h4
          className="text-display-l font-bold uppercase"
          data-testid="manual-trading-total-value"
        >
          {formatUsd(0)}
        </h4>
        <p
          className="text-md font-medium text-white/60"
          data-testid="manual-trading-total-label"
        >
          {t('manual-trading.total-value-of-portfolio')}
        </p>
      </div>
      <div className="grid grid-cols-1 gap-1 @[370px]/POC:grid-cols-2">
        <ProfitCard
          testId="manual-trading-profit-value"
          title={t('manual-trading.profit')}
          trend={'0'}
          value={'0'}
        />
        <ProfitCard
          testId="manual-trading-1d-change-value"
          title={t('period-change.1d')}
          trend={'0'}
          value={'0'}
        />
      </div>
    </Card>
  );
};
