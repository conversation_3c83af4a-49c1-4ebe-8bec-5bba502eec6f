import { DEFAULT_PAGINATION_SIZE, REFETCH_INTERVAL_15_SECONDS } from '@/api/constants';
import { useSearchUserTransactionOfAllTokenV3Infinite } from '@/lib/api';

import { lastTransactionsRequestParams } from './last-transactions-request-params';

export const useLastTransactionsInfinite = (walletId?: string, searchString?: string, size = DEFAULT_PAGINATION_SIZE) =>
  useSearchUserTransactionOfAllTokenV3Infinite(
    lastTransactionsRequestParams({
      walletId,
      searchString,
      useSelectedChains: true,
    }),
    { size },
    {
      query: {
        initialPageParam: undefined,
        refetchInterval: REFETCH_INTERVAL_15_SECONDS,
        refetchOnWindowFocus: true,
        staleTime: REFETCH_INTERVAL_15_SECONDS,
        getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId : undefined),
      },
    },
  );
