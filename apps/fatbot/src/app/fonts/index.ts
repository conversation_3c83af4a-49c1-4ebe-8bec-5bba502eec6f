import localFont from 'next/font/local';

export const goldplay = localFont({
  src: [
    {
      path: './Goldplay-Medium.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: './Goldplay-SemiBold.woff2',
      weight: '600',
      style: 'normal',
    },
    {
      path: './Goldplay-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
    {
      path: './Goldplay-Black.woff2',
      weight: '900',
      style: 'normal',
    },
  ],
  preload: true,
  fallback: ['system-ui', 'sans-serif'],
  variable: '--font-goldplay',
});

export const sailec = localFont({
  src: [
    {
      path: './Sailec-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: './Sailec-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
  ],
  preload: true,
  fallback: ['system-ui', 'sans-serif'],
  variable: '--font-sailec',
});
