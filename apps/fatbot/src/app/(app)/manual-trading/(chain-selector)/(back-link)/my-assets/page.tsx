import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getGetAllUserWalletsV3QueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { MyAssetsDetailContent } from '@/module/manual-trading/my-assets-detail/my-assets-detail-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('my-assets'),
  };
}

export default async function MyAssetsDetailPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(
    getGetAllUserWalletsV3QueryOptions({ useSelectedChains: true })
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <MyAssetsDetailContent />
    </HydrationBoundary>
  );
}
