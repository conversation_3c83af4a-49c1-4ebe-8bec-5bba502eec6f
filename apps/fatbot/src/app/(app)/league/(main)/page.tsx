import { getTranslations } from 'next-intl/server';

import { getGetUserLeaderboardInfoQueryOptions, getSearchUserLeaderboardsInfiniteQueryOptions } from '@/lib/api';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { getQueryClient } from '@/lib/query-client';
import { LeagueHeader } from '@/module/league/components/league-header';
import { LeagueAuthenticatedContent } from '@/module/league/league-authenticated-content';
import { LeagueComingSoonContent } from '@/module/league/league-coming-soon-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('league.root'),
  };
}

export default async function LeaguePage() {
  const queryClient = getQueryClient();
  const leagueSystemEnabled = await getRemoteConfigValue('league_system_enabled');

  await Promise.all([
    queryClient.prefetchQuery(getGetUserLeaderboardInfoQueryOptions()),
    queryClient.prefetchInfiniteQuery(
      getSearchUserLeaderboardsInfiniteQueryOptions({ size: 5 }, { query: { getNextPageParam: () => undefined } }),
    ),
  ]);

  return (
    <>
      <LeagueHeader />
      {leagueSystemEnabled ? <LeagueAuthenticatedContent /> : <LeagueComingSoonContent />}
    </>
  );
}
