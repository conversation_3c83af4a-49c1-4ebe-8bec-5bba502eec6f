import Link from 'next/link';
import { getTranslations } from 'next-intl/server';

import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { isRight } from '@/lib/either';
import { AuthCard } from '@/module/auth/auth-card';

import { handleApplyActionCode } from './handle-apply-action-code';
import { NewVerifyEmailLink } from './new-verify-email-link';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('verify-email'),
  };
}

interface Props {
  searchParams: Promise<{ oobCode?: string; user: string }>;
}

export default async function VerifyEmail(props: Props) {
  const searchParams = await props.searchParams;
  const t = await getTranslations('verify-email');

  const result = await handleApplyActionCode(searchParams.oobCode ?? '');

  return (
    <AuthCard className="m-auto">
      {isRight(result) ? (
        <>
          <Display className="text-center" size="M">
            {t('confirmation')}
          </Display>
          <p className="text-center text-text-secondary">
            {t('confirmation-text')}
          </p>
          <Button asChild className="w-full">
            <Link href={ROUTES.LOGIN}>{t('cta')}</Link>
          </Button>
        </>
      ) : (
        <NewVerifyEmailLink user={searchParams.user} />
      )}
    </AuthCard>
  );
}
