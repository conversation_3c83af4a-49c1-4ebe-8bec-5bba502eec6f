'use server';

import * as Sentry from '@sentry/nextjs';
import { FirebaseError } from 'firebase/app';
import { confirmPasswordReset, getAuth, validatePassword, verifyPasswordResetCode } from 'firebase/auth';
import { cookies, headers } from 'next/headers';
import { refreshCookiesWithIdToken } from 'next-firebase-auth-edge/lib/next/cookies';
import { getTranslations } from 'next-intl/server';

import { handleReSendEmailVerification } from '@/api';
import {
  DEPOSIT_DIALOG_SHOWN_COOKIE_NAME,
  HIDE_ONBOARDING_COOKIE_NAME,
  REFERRAL_CODE_COOKIE_NAME,
  SHOW_LOGIN_COOKIE_NAME,
} from '@/constants/config';
import { SENTRY_ERRORS } from '@/constants/sentry';
import { env } from '@/env/client';
import { app } from '@/firebase';
import { getFirebaseAdmin } from '@/lib/firebase/get-firebase-admin';
import { getFirebaseErrorMessage } from '@/lib/firebase/get-firebase-error-message';
import { authConfig } from '@/lib/firebase/server-config';
import { sendMailBase } from '@/lib/mails/constants';
import { getEcomailConfig } from '@/lib/mails/get-ecomail-config';
import { sendMail } from '@/lib/mails/send-mail';

// because of security reasons we always return success response
// if exception is thrown we send log to sentry
export async function requestResetPasswordLink(email: string) {
  try {
    const admin = getFirebaseAdmin();
    const t = await getTranslations('emails');

    const resetpasswordLink = await admin.auth().generatePasswordResetLink(email);

    const { resetPasswordTemplateId } = getEcomailConfig();

    await sendMail({
      message: {
        ...sendMailBase,
        template_id: resetPasswordTemplateId,
        subject: t('reset-your-password'),
        to: [{ email, name: email }],
        global_merge_vars: [
          { name: 'EMAIL', content: email },
          { name: 'NAME', content: resetpasswordLink },
        ],
      },
    });
    return true;
  } catch (error) {
    if (error instanceof Error || error instanceof FirebaseError) {
      const message = await getFirebaseErrorMessage(error);
      const isEmailNotFoundError = message.includes('create the email action link');

      if (!isEmailNotFoundError) {
        // unknown error, send to sentry
        Sentry.captureException(new Error(SENTRY_ERRORS.REQUEST_RESET_ERROR), {
          extra: { originalError: error },
        });
      }
      // email not found error
      return true;
    }

    // unknown error, send to sentry
    Sentry.captureException(new Error(SENTRY_ERRORS.REQUEST_RESET_ERROR), {
      extra: { originalError: error },
    });
    return true;
  }
}

export async function finalizeLogin(idToken: string) {
  await refreshCookiesWithIdToken(idToken, await headers(), await cookies(), authConfig);

  (await cookies()).set(SHOW_LOGIN_COOKIE_NAME, 'true');
}

export async function signUpAction(idToken: string, email: string | null) {
  try {
    if (!email) {
      return;
    }

    const admin = getFirebaseAdmin();
    const referralCode = (await cookies()).get(REFERRAL_CODE_COOKIE_NAME)?.value;
    (await cookies()).set(DEPOSIT_DIALOG_SHOWN_COOKIE_NAME, 'false');

    await fetch(`${env.NEXT_PUBLIC_API_BASE_URL}/users/sign-up${referralCode ? `?referralCode=${referralCode}` : ''}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${idToken}`,
      },
    });

    const verificationUrl = await admin.auth().generateEmailVerificationLink(email);

    const { verifyEmailTemplateId } = getEcomailConfig();

    const t = await getTranslations('emails');

    const response = await sendMail({
      message: {
        ...sendMailBase,
        template_id: verifyEmailTemplateId,
        subject: t('verify-your-email'),
        to: [{ email, name: email }],
        global_merge_vars: [
          { name: 'EMAIL', content: email },
          { name: 'NAME', content: verificationUrl },
        ],
      },
    });

    if (response) {
      return;
    }

    (await cookies()).set(SHOW_LOGIN_COOKIE_NAME, 'true');
  } catch (error) {
    if (error instanceof Error || error instanceof FirebaseError) {
      return {
        error: await getFirebaseErrorMessage(error),
      };
    } else {
      return {
        error: 'Some unknown error ocurred.',
      };
    }
  }
}

export async function resendVerifyEmailLink(email: string, password: string) {
  try {
    await handleReSendEmailVerification(email, password);
  } catch (error) {
    if (error instanceof Error || error instanceof FirebaseError) {
      return {
        error: await getFirebaseErrorMessage(error),
      };
    } else {
      return {
        error: 'Some unknown error ocurred.',
      };
    }
  }
}

export async function resetPassword(_: string, password: string, oobCode: string) {
  try {
    const response = await validatePassword(getAuth(app), password);
    const check = Object.values(response).every((value) => value);

    if (check) {
      await confirmPasswordReset(getAuth(app), oobCode, password);
    } else {
      throw new FirebaseError('auth/password-policy', '');
    }
  } catch (error) {
    if (error instanceof Error || error instanceof FirebaseError) {
      return {
        error: await getFirebaseErrorMessage(error),
      };
    } else {
      return {
        error: 'Some unknown error ocurred.',
      };
    }
  }
}

export async function hideOnboarding() {
  (await cookies()).set(HIDE_ONBOARDING_COOKIE_NAME, 'true');
}

export async function verifyOobCode(oobCode: string) {
  try {
    await verifyPasswordResetCode(getAuth(app), oobCode);

    return true;
  } catch {
    return false;
  }
}

export async function disableMfa(uid: string) {
  const admin = getFirebaseAdmin();

  try {
    await admin.auth().updateUser(uid, {
      multiFactor: {
        enrolledFactors: null,
      },
    });
    return true;
  } catch {
    return false;
  }
}
