import { useEffect, useState } from 'react';

// based on tailwind breakpoints
const breakpoints = (mediaQuery: string): string => {
  switch (mediaQuery) {
    case 'xxlarge':
      return '(min-width: 1536px)'; // 96rem and above
    case 'xlarge':
      return '(max-width: 1535px)'; // up to 96rem
    case 'large':
      return '(max-width: 1279px)'; // up to 80rem
    case 'medium':
      return '(max-width: 1023px)'; // up to 64rem
    case 'small':
      return '(max-width: 767px)'; // up to 48rem
    case 'tiny':
      return '(max-width: 639px)'; // up to 40rem
    default:
      return mediaQuery;
  }
};

type MediaQuery = 'large' | 'medium' | 'small' | 'tiny' | 'xlarge' | 'xxlarge';

export const useMediaQueryMatch = (mediaQuery: MediaQuery): boolean => {
  const [mediaMatch, setMediaMatch] = useState<boolean>(false);

  useEffect(() => {
    const matchMediaAPI = window.matchMedia(breakpoints(mediaQuery));
    const updateScreenSize = (event: MediaQueryListEvent): void => {
      setMediaMatch(event.matches);
    };

    setMediaMatch(matchMediaAPI.matches);

    matchMediaAPI.addEventListener('change', updateScreenSize);
    return () => {
      matchMediaAPI.removeEventListener('change', updateScreenSize);
    };
  }, [mediaQuery]);

  return mediaMatch;
};
