import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';

const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      'font-size': ['text-xxs', 'text-xs', 'text-sm', 'text-md', 'text-lg', 'text-xl'],
    },
  },
});

/**
 * Combines multiple class names into a single string.
 * Tailwind classes are merged without conflicts using the `tailwind-merge` package.
 *
 * @param inputs - The class names to be combined.
 * @returns The combined class names as a string.
 */
export const cn = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

/**
 * Copies text to the clipboard.
 * @param text - The text to be copied.
 */
export const copyToClipboard = (text: string) => navigator.clipboard.writeText(text);
