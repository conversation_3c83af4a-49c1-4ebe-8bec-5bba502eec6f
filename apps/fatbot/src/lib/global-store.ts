import { createJSONStorage, persist } from 'zustand/middleware';
import { createStore } from 'zustand/vanilla';

export interface GlobalState {
  hideOnboarding?: boolean;
}

export interface GlobalActions {
  setHideOnboarding: (hideOnboarding: boolean) => void;
}

export type GlobalStore = GlobalActions & GlobalState;

export const initGlobalStore = (): GlobalState => ({ hideOnboarding: undefined });

export const defaultInitState: GlobalState = {
  hideOnboarding: undefined,
};

export const createGlobalStore = (initState: GlobalState = defaultInitState) =>
  createStore<GlobalStore>()(
    persist(
      (set) => ({
        ...initState,
        setHideOnboarding: (hideOnboarding) => {
          set(() => ({ hideOnboarding: hideOnboarding }));
        },
      }),
      {
        name: 'store',
        storage: createJSONStorage(() => localStorage),
      },
    ),
  );
