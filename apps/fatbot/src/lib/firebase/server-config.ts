import { env } from '@/env/server';

export const COOKIE_NAME = 'AuthToken';

export const serverConfig = {
  useSecureCookies: env.USE_SECURE_COOKIES,
  firebaseApiKey: env.FIREBASE_API_KEY,
  serviceAccount: {
    projectId: env.FIREBASE_PROJECT_ID,
    clientEmail: env.FIREBASE_ADMIN_CLIENT_EMAIL,
    privateKey: env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n'),
  },
};

// Twelve Days
const COOKIE_MAX_AGE = 1_036_800;

export const authConfig = {
  apiKey: serverConfig.firebaseApiKey,
  cookieName: COOKIE_NAME,
  cookieSignatureKeys: [env.COOKIE_SECRET_CURRENT, env.COOKIE_SECRET_PREVIOUS],
  cookieSerializeOptions: {
    path: '/',
    httpOnly: true,
    secure: serverConfig.useSecureCookies, // Set this to true on HTTPS environments
    sameSite: 'lax' as const,
    maxAge: COOKIE_MAX_AGE,
  },
  serviceAccount: serverConfig.serviceAccount,
  // Set to false in Firebase Hosting environment due to https://stackoverflow.com/questions/********/firebase-cloud-function-wont-store-cookie-named-other-than-session
  enableMultipleCookies: true,
  experimental_enableTokenRefreshOnExpiredKidHeader: true,
  debug: false,
};
