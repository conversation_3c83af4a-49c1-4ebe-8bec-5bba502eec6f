import type { Chain } from '@/lib/api';
import { createSearchParams } from '@/utils/search-params';

export type RouteValue =
  | string
  | {
      ROOT: string | ((redirectUrl?: string) => string);
      [key: string]: unknown;
    };

export type Routes = Record<string, RouteValue>;

export const ROUTES = {
  HOME: '/',
  ASSET_ALLOCATION: {
    ROOT: '/asset-allocation',
    MANUALLY_TRADED: (redirectUrl?: string) => `/asset-allocation/manual-traded${createSearchParams({ redirectUrl })}`,
  },
  SIGN_UP: '/auth',
  MANUAL_TRADING: {
    ROOT: '/manual-trading',
    TOKEN_DETAIL: (address: string, chain: Chain, redirectUrl?: string) =>
      `/manual-trading/${chain}/${address}${createSearchParams({
        redirectUrl,
      })}`,
  },
  LOGOUT: '/auth/logout',
  LOGIN: '/auth?type=login',
  PRIVACY_POLICY: '/privacy-policy',
  TERMS_AND_CONDITIONS: '/terms-and-conditions',
  RESET_PASSWORD: '/reset-password',
  VERIFY_EMAIL: '/verify-email',
  WALLETS: {
    ROOT: '/wallets',
    DETAIL: (walletId: string, selectedChain?: string, redirectUrl?: string) =>
      `/wallets/${walletId}${createSearchParams({ redirectUrl, selectedChain })}`,
    WITHDRAW: (walletId: string, showChooseToken?: string, redirectUrl?: string) =>
      `/wallets/${walletId}/withdraw${createSearchParams({
        redirectUrl,
        showChooseToken,
      })}`,
  },
  TRADE: '/trade',
  PROFILE: {
    ROOT: '/profile',
    REFERRAL: {
      ROOT: '/profile/referral',
      INVITE: '/profile/referral/invite',
    },
    SETUP_MFA: {
      ROOT: '/profile/setup-mfa',
    },
  },
  SETUP_WALLET: {
    ROOT: '/wallets/setup-wallet',
    path: (selectedChain?: string, redirectUrl?: string) =>
      `/wallets/setup-wallet${createSearchParams({ selectedChain, redirectUrl })}`,
    IMPORT: (selectedChain?: string, redirectUrl?: string) =>
      `/wallets/setup-wallet/import${createSearchParams({ selectedChain, redirectUrl })}`,
    CREATE: (selectedChain?: string, redirectUrl?: string) =>
      `/wallets/setup-wallet/create${createSearchParams({ selectedChain, redirectUrl })}`,
    DONE: (type: 'create' | 'import', walletAddress?: string, selectedChain?: string, redirectUrl?: string) =>
      `/wallets/setup-wallet/done${createSearchParams({
        type,
        walletAddress,
        selectedChain,
        redirectUrl,
      })}`,
  },
  BOT_TRADING: {
    ROOT: '/bot-trading',
    BOT_TRANSACTIONS: (botId: string, redirectUrl?: string) =>
      `/bot-trading/transactions${createSearchParams({
        redirectUrl,
        botId,
      })}`,
    BOT_WITHDRAW: (botId: string, redirectUrl?: string) =>
      `/bot-trading/withdraw/${botId}${createSearchParams({ redirectUrl })}`,
    BOT_LEADERBOARD: (redirectUrl?: string) => `/bot-trading/leaderboard${createSearchParams({ redirectUrl })}`,
    BOT_PORTFOLIO: (botId?: string) => (botId ? `/bot-trading/portfolio?botId=${botId}` : '/bot-trading/portfolio'),
    BOT_TRADES: (botId: string, redirectUrl?: string) =>
      `/bot-trading/trades${createSearchParams({
        redirectUrl,
        botId,
      })}`,
    BOT_TRADE_DETAIL: (botId: string, tradeId: string, chain: Chain, tokenAddress: string, redirectUrl?: string) =>
      `/bot-trading/trades/${botId}/${tradeId}/${chain}/${tokenAddress}${createSearchParams({
        redirectUrl,
      })}`,
    BOT_COMPARE_ACTIVITY: (redirectUrl?: string) =>
      `/bot-trading/compare/activity${createSearchParams({ redirectUrl })}`,
    BOT_COMPARE_SETTINGS: '/bot-trading/compare/settings',
    BOT_COMPARE_ALLOCATION: '/bot-trading/compare/portfolio-allocation',
    DETAIL: (botId: string, redirectUrl?: string) =>
      `/bot-trading/detail/${botId}${createSearchParams({ redirectUrl })}`,
    SETTINGS: (botId: string) => `/bot-trading/detail/${botId}/settings`,
    ACTIVE_BOTS_SELECTION: (botIdToBeCreated: string) => `/bot-trading/select-active-bots/${botIdToBeCreated}`,
    LAUNCH: (botId: string, redirectUrl?: string) =>
      `/bot-trading/launch/${botId}${createSearchParams({ redirectUrl })}`,
    BOT_WIZARD: (
      params: {
        botDraftId?: string;
        botId?: string;
        redirectUrl?: string;
        step?: string;
      } = {},
    ) => `/bot-trading/wizard${createSearchParams(params)}`,
    BOT_DEPOSIT_TO_WALLET: (redirectUrl?: string) =>
      `/bot-trading/deposit-to-wallet${createSearchParams({ redirectUrl })}`,
  },
  LAST_TRANSACTIONS: {
    ROOT: '/manual-trading/last-transactions',
    path: (walletId?: string, redirectUrl?: string) =>
      `/manual-trading/last-transactions${createSearchParams({
        walletId,
        redirectUrl,
      })}`,
  },
  MY_ASSETS: {
    ROOT: '/manual-trading/my-assets',
    path: (walletId?: string, redirectUrl?: string) =>
      `/manual-trading/my-assets${createSearchParams({ walletId, redirectUrl })}`,
    search: (viewState: 'search' | 'select', redirectUrl?: string) =>
      `/manual-trading/my-assets${createSearchParams({ viewState, redirectUrl })}`,
  },
  DEPOSIT_TO_WALLET: {
    ROOT: '/manual-trading/deposit-to-wallet',
    path: (walletId?: string) => `/manual-trading/deposit-to-wallet${createSearchParams({ walletId })}`,
  },
  HOT_TOKENS: {
    ROOT: '/manual-trading/hot-tokens',
    path: (walletId?: string, redirectUrl?: string) =>
      `/manual-trading/hot-tokens${createSearchParams({
        walletId,
        redirectUrl,
      })}`,
  },
  LEAGUE: {
    ROOT: '/league',
    LEADERBOARD: (redirectUrl?: string) => `/league/leaderboard${createSearchParams({ redirectUrl })}`,
    LEADERBOARD_DETAIL: (redirectUrl?: string) => `/league/leaderboard/detail${createSearchParams({ redirectUrl })}`,
    CLAIMING: '/league/claiming',
  },
} satisfies Routes;
