import { TimeRange } from '@/lib/api';

import { generateEmptyAreaData, generateYAxisStepSize } from './utils';

// Mock the dependencies that use ES modules
vi.mock('@/lib/api', () => ({
  TimeRange: {
    HOUR: 'HOUR',
    DAY: 'DAY',
    WEEK: 'WEEK',
    MONTH: 'MONTH',
    YEAR: 'YEAR',
    ALL: 'ALL',
  },
}));

describe('generateYAxisStepSize', () => {
  it('should handle normal range', () => {
    const result = generateYAxisStepSize({ min: 0, max: 100 });
    expect(result).toBe(20); // Should create 5 steps: 0, 20, 40, 60, 80, 100
  });

  it('should handle small range', () => {
    const result = generateYAxisStepSize({ min: 0, max: 1 });
    expect(result).toBe(0.2); // Should create 5 steps: 0, 0.2, 0.4, 0.6, 0.8, 1
  });

  it('should handle large range', () => {
    const result = generateYAxisStepSize({ min: 0, max: 1000000 });
    expect(result).toBe(200000); // Should create 5 steps: 0, 200k, 400k, 600k, 800k, 1M
  });

  it('should handle zero range', () => {
    const result = generateYAxisStepSize({ min: 5, max: 5 });
    expect(result).toBe(0.1); // Default step size for zero range
  });

  it('should handle negative range', () => {
    const result = generateYAxisStepSize({ min: 10, max: 5 });
    expect(result).toBe(0.1); // Default step size for negative range
  });
});

describe('generateEmptyAreaData', () => {
  it('should generate data for HOUR range', () => {
    const data = generateEmptyAreaData(TimeRange.HOUR);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data for DAY range', () => {
    const data = generateEmptyAreaData(TimeRange.DAY);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data for WEEK range', () => {
    const data = generateEmptyAreaData(TimeRange.WEEK);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data for MONTH range', () => {
    const data = generateEmptyAreaData(TimeRange.MONTH);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data for YEAR range', () => {
    const data = generateEmptyAreaData(TimeRange.YEAR);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data for ALL range', () => {
    const data = generateEmptyAreaData(TimeRange.ALL);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should generate data with custom number of months', () => {
    const data = generateEmptyAreaData(TimeRange.ALL, 12);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]?.value).toBe(0);
    expect(data[data.length - 1]?.value).toBe(0);
  });

  it('should have valid timestamps', () => {
    const data = generateEmptyAreaData(TimeRange.DAY);
    expect(data.every((item) => typeof item.time === 'number')).toBe(true);
    expect(data.every((item) => Number(item.time) > 0)).toBe(true);
  });
});
