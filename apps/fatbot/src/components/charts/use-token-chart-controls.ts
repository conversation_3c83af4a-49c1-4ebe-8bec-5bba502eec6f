'use client';

import { useEffect } from 'react';

import type { ChartType } from '@/components/charts/use-token-chart-store';
import { useTokenChartStore } from '@/components/charts/use-token-chart-store';
import { Chain, TimeRange } from '@/lib/api';
import { sessionStorageClient } from '@/utils/session-storage';

export const useTokenChartControls = (chain: Chain) => {
  const chartType = useTokenChartStore.use.chartType();
  const timeRange = useTokenChartStore.use.timeRange();
  const chartKey = useTokenChartStore.use.chartKey();
  const setChartType = useTokenChartStore.use.setChartType();
  const setTimeRange = useTokenChartStore.use.setTimeRange();
  const setChartKey = useTokenChartStore.use.setChartKey();

  // set default time range for different chains
  useEffect(() => {
    setTimeRange(chain === Chain.SOLANA ? TimeRange.HOUR : TimeRange.MONTH);
  }, [chain, setTimeRange]);

  // fixes hydration error
  useEffect(() => {
    setChartType((sessionStorageClient.getItem('token-detail-chart-key') ?? 'advanced') as ChartType);
  }, [setChartType]);

  return {
    timeRange,
    setTimeRange,
    chartKey,
    setChartKey,
    chartType,
    setChartType,
  };
};
