'use client';

import React, { type FunctionComponent } from 'react';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import type { Chain } from '@/lib/api';
import { cn } from '@/lib/utils';
import { ChainIcon } from '@/module/multichain/chain-icon';

interface Props {
  chain: Chain;
  title: string;
  titleSuffix?: React.ReactNode;
  subtitle: string;
  active?: boolean;
  caption?: string;
  className?: string;
  onClick?: () => void;
  leadingContent?: React.ReactNode;
}

export const OptionCard: FunctionComponent<Props> = ({
  chain,
  active,
  caption,
  className,
  onClick,
  subtitle,
  title,
  titleSuffix,
  leadingContent,
}) => (
  <div
    className={cn(
      'flex cursor-pointer select-none items-center justify-between gap-2 rounded-md border-2 border-border-primary/10 px-2 py-1 transition-colors hover:border-border-active',
      {
        'border-surface-brand-1-alpha-chips bg-primary/10 text-text-active': active,
      },
      className,
    )}
    tabIndex={0}
    onClick={onClick}
  >
    <div className="flex items-center gap-x-2">
      {leadingContent}
      <div className="flex flex-col gap-0.5">
        <h2 className="flex items-center gap-x-1 text-md font-bold">
          <ChainIcon chain={chain} className="size-2" />
          {title} {titleSuffix}
        </h2>
        <span
          className={cn('text-sm font-medium', {
            'text-text-active': active,
          })}
        >
          {subtitle}
        </span>
      </div>
    </div>

    <RadioGroup className="flex items-center gap-2">
      <span
        className={cn('text-sm font-medium', {
          'text-text-active': active,
        })}
      >
        {caption}
      </span>
      <RadioGroupItem checked={active} value={title} />
    </RadioGroup>
  </div>
);
