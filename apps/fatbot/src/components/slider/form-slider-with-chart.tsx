'use client';

import { type FC } from 'react';

import { AlertErrorMessage } from '@/components/alert-message';
import type { SliderChartData } from '@/components/charts/slider-chart/utils';
import { FormField } from '@/components/form/form';
import { Input } from '@/components/form/input';
import { NumericInput } from '@/components/form/numeric-input';
import { cn } from '@/lib/utils';

import { InfiniteValueInput } from './infinite-value-input';
import { SliderWithChart } from './slider-with-chart';
import {
  getSliderPropsByType,
  isSingleValueSlider,
  type RangeValueSliderProps,
  type SingleValueSliderProps,
} from './types';
import {
  getFormattedChartInputValue,
  getSliderNumberValues,
  getSliderRange,
  STRING_INFINITY,
  useSliderInputs,
} from './utilities';

const DEFAULT_PERCENTAGE = 0.01;

export type Props = {
  name: string;
  displayData: SliderChartData;
  minInputPlaceholder?: string;
  maxInputPlaceholder?: string;
  inputPrefix?: string;
  inputSuffix?: string;
  includeInfinityValue?: boolean;
  step?: number;
  displayPercentage?: boolean;
  error?: string;
  infiniteValuePlaceholder?: string;
} & (RangeValueSliderProps | SingleValueSliderProps);

export const FormSliderWithChart: FC<Props> = ({
  name,
  displayData,
  minInputPlaceholder,
  maxInputPlaceholder,
  inputPrefix,
  type,
  displayAs,
  includeInfinityValue,
  step,
  inputSuffix = '',
  displayPercentage,
  error,
  infiniteValuePlaceholder = STRING_INFINITY,
}) => {
  const { min: sliderMin, max: sliderMax, gap } = getSliderRange(displayData.map(({ valueFrom }) => valueFrom));

  const {
    startValueNumber,
    endValueNumber,
    onStartValueChange,
    onEndValueChange,
    generatedInputKey,
    startInputRef,
    endInputRef,
    trigger,
    focusedInput,
    clearFocus,
    onStartInputFocus,
    onEndInputFocus,
  } = useSliderInputs({ name, type, sliderMax: sliderMax ?? 0, displayPercentage });

  const renderEndInfiniteInput = endValueNumber === Infinity && focusedInput !== 'end';
  const renderStartInfiniteInput = startValueNumber === Infinity && focusedInput !== 'start';

  return (
    <div>
      <FormField
        name={name}
        render={({ field }) => {
          const numberValues = getSliderNumberValues(field.value);

          return (
            <>
              <SliderWithChart
                data={displayData}
                defaultValue={numberValues}
                gap={gap}
                max={includeInfinityValue ? (sliderMax ?? 0) + 1 : (sliderMax ?? 0)}
                min={sliderMin ?? 0}
                step={displayPercentage ? DEFAULT_PERCENTAGE : step}
                value={numberValues}
                onValueChange={(value) => {
                  const [start, end] = value;

                  // if type is 'singleValue' only startValue is rendered
                  if (isSingleValueSlider(type)) {
                    field.onChange([start?.toString()]);
                  } else {
                    field.onChange([
                      start?.toString(),
                      (end ?? 0) > (sliderMax ?? 0) ? STRING_INFINITY : end?.toString(),
                    ]);
                  }

                  void trigger(name);
                }}
                {...getSliderPropsByType(type, displayAs)}
              />
            </>
          );
        }}
      />

      <div
        className={cn('grid grid-cols-2 gap-x-3 pb-2 pt-4', {
          'grid-cols-1': type === 'singleValue',
        })}
      >
        <div>
          {renderStartInfiniteInput ? (
            <InfiniteValueInput
              className="font-semibold"
              displayValue={infiniteValuePlaceholder}
              placeholder={minInputPlaceholder}
              onFocus={onStartInputFocus}
            />
          ) : (
            <NumericInput
              key={`input-start-${generatedInputKey}`}
              allowNegative={false}
              autoFocus={focusedInput === 'start'}
              className="font-semibold"
              customInput={Input}
              getInputRef={startInputRef}
              inputMode="decimal"
              max={isNaN(Number(endValueNumber)) ? sliderMax : endValueNumber}
              min={sliderMin}
              placeholder={minInputPlaceholder}
              prefix={inputPrefix}
              suffix={isSingleValueSlider(type) && startValueNumber === Infinity ? '' : inputSuffix}
              value={getFormattedChartInputValue(startValueNumber, displayPercentage)}
              onBlur={clearFocus}
              onFocus={onStartInputFocus}
              onValueChange={onStartValueChange}
            />
          )}
        </div>

        {!isSingleValueSlider(type) ? (
          <div>
            {renderEndInfiniteInput ? (
              <InfiniteValueInput
                className="font-semibold"
                displayValue={infiniteValuePlaceholder}
                placeholder={maxInputPlaceholder}
                onFocus={onEndInputFocus}
              />
            ) : (
              <NumericInput
                key={`input-end-${generatedInputKey}`}
                allowNegative={false}
                autoFocus={focusedInput === 'end'}
                className="font-semibold"
                customInput={Input}
                getInputRef={endInputRef}
                inputMode="decimal"
                min={isNaN(startValueNumber) ? sliderMin : startValueNumber}
                placeholder={maxInputPlaceholder}
                prefix={inputPrefix}
                suffix={endValueNumber === Infinity ? '' : inputSuffix}
                value={getFormattedChartInputValue(endValueNumber, displayPercentage)}
                onBlur={clearFocus}
                onFocus={onEndInputFocus}
                onValueChange={onEndValueChange}
              />
            )}
          </div>
        ) : null}
        {error ? <AlertErrorMessage className="-mt-2" customError={error} inputName={name} /> : null}
      </div>
    </div>
  );
};
