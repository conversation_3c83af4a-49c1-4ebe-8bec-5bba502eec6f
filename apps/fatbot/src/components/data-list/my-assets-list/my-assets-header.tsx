import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';

interface Props {
  className?: string;
}

export const MyAssetsHeader: React.FC<Props> = ({ className }) => {
  const t = useTranslations('common');

  return (
    <div className="@container/mth">
      <div
        className={cn(
          'grid w-full grid-cols-[8fr_4fr] items-center gap-0 rounded-xs text-text-secondary @-[700px]/mth:grid-cols-[5fr_1.5fr_1.5fr_1.5fr_3fr] @-[1000px]/mth:grid-cols-[5fr_1.5fr_1.5fr_1.5fr_2.5fr]',
          className,
        )}
      >
        <div className="flex justify-start">{t('asset')}</div>
        <div className="hidden justify-end @[700px]/mth:flex">{t('price')}</div>
        <div className="hidden justify-end @[700px]/mth:flex">{t('1h')}</div>
        <div className="hidden justify-end @[700px]/mth:flex">{t('24h')}</div>
        <div className="flex justify-end">{t('performance')}</div>
      </div>
    </div>
  );
};
