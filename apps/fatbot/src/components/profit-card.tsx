import type { ReactNode } from 'react';

import { Card } from '@/components/ui/card';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';

import { Trend } from './trend';

interface ProfitCardProps {
  title: ReactNode;
  value: string;
  trend?: string;
  className?: string;
  onClick?: () => void;
  testId?: string;
}

export const ProfitCard = ({ title, trend, value, className, onClick, testId }: ProfitCardProps) => (
  <Card
    className={cn('flex-col gap-1 shadow-primary', { 'cursor-pointer': !!onClick }, className)}
    data-testid={testId}
    variant="primary"
    onClick={onClick}
  >
    <span className="text-sm font-medium text-text-elevated">{title}</span>
    <span
      className="overflow-hidden text-ellipsis text-display-xs font-bold text-text-primary"
      data-testid="profit-card-value"
    >
      {formatUsd(value)}
    </span>
    {trend ? (
      <Trend
        badgeProps={{
          size: 'small',
        }}
        value={trend}
      />
    ) : null}
  </Card>
);
