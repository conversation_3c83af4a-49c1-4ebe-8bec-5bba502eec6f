'use client';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { Cross2Icon } from '@radix-ui/react-icons';
import { cva } from 'class-variance-authority';
import { useTranslations } from 'next-intl';
import type { ComponentProps, HTMLAttributes } from 'react';

import { ArrowLeft } from '@/assets';
import { cn } from '@/lib/utils';

import { Button } from './button';

const Dialog = DialogPrimitive.Root;

const DialogTrigger = DialogPrimitive.Trigger;

const DialogPortal = DialogPrimitive.Portal;

const DialogClose = DialogPrimitive.Close;

const DialogOverlay = ({ className, ...props }: ComponentProps<typeof DialogPrimitive.Overlay>) => (
  <DialogPrimitive.Overlay
    className={cn(
      'fixed inset-0 z-50 bg-secondary-deep/60 backdrop-blur-xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className,
    )}
    {...props}
  />
);
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const dialogContentVariants = cva(
  'fixed top-0 z-50 flex size-full max-h-screen flex-col gap-4 overflow-auto px-3 pt-8 shadow-lg outline-hidden duration-200 sm:left-1/2 sm:top-1/2 sm:h-auto sm:max-w-[402px] sm:-translate-x-1/2 sm:-translate-y-1/2 sm:gap-3 sm:rounded-lg sm:p-3 sm:data-[state=open]:animate-in sm:data-[state=closed]:animate-out sm:data-[state=closed]:fade-out-0 sm:data-[state=open]:fade-in-0 sm:data-[state=closed]:zoom-out-95 sm:data-[state=open]:zoom-in-95 sm:data-[state=closed]:slide-out-to-left-1/2 sm:data-[state=closed]:slide-out-to-top-[48%] sm:data-[state=open]:slide-in-from-left-1/2 sm:data-[state=open]:slide-in-from-top-[48%]',
  {
    variants: {
      bgVariant: {
        default: 'bg-surface-background sm:bg-surface-primary',
        secondary: 'bg-surface-area',
        tertiary: 'bg-surface-brand-2',
        quaternary: 'bg-surface-brand-1',
        quinary: 'bg-surface-background',
      },
      invert: {
        true: '', // handled by compoundVariants
        false: '',
      },
    },
    compoundVariants: [
      {
        invert: true,
        class: 'bg-surface-active sm:bg-surface-active',
      },
    ],
    defaultVariants: {
      bgVariant: 'default',
      invert: false,
    },
  },
);

type DialogContentProps = ComponentProps<typeof DialogPrimitive.Content> & {
  hideClose?: boolean;
  invert?: boolean;
  elevatedCloseButton?: boolean;
  bgVariant?: 'default' | 'quaternary' | 'quinary' | 'secondary' | 'tertiary';
  overlayClassName?: string;
};

const DialogContent = ({
  className,
  hideClose,
  invert = false,
  children,
  elevatedCloseButton,
  bgVariant = 'default',
  overlayClassName,
  ...props
}: DialogContentProps) => {
  const t = useTranslations();
  return (
    <DialogPortal>
      <DialogOverlay className={overlayClassName} />
      <DialogPrimitive.Content
        className={cn(
          invert ? dialogContentVariants({ invert: true }) : dialogContentVariants({ bgVariant, invert: false }),
          className,
        )}
        onOpenAutoFocus={(event) => {
          event.preventDefault();
        }}
        {...props}
      >
        {!hideClose ? (
          <DialogPrimitive.Close className={cn('absolute left-3 top-2 sm:hidden', elevatedCloseButton && 'z-20')}>
            <Button asChild size="icon" variant={invert ? 'light' : 'secondary'}>
              <div>
                <ArrowLeft className="size-3" />
              </div>
            </Button>
            <span className="sr-only">{t('close')}</span>
          </DialogPrimitive.Close>
        ) : null}
        {children}
        {!hideClose ? (
          <DialogPrimitive.Close
            className={cn(
              'absolute right-3 top-3 hidden opacity-70 ring-offset-white transition-opacity hover:opacity-100 disabled:pointer-events-none data-[state=open]:bg-neutral-100 data-[state=open]:text-neutral-500 dark:ring-offset-neutral-950 dark:focus:ring-neutral-300 dark:data-[state=open]:bg-neutral-800 dark:data-[state=open]:text-neutral-400 sm:block',
              invert && 'text-text-invert',
            )}
          >
            <Cross2Icon className="size-3" />
            <span className="sr-only">{t('close')}</span>
          </DialogPrimitive.Close>
        ) : null}
      </DialogPrimitive.Content>
    </DialogPortal>
  );
};
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogHeader = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('flex flex-col space-y-1.5 text-center sm:text-left', className)} {...props} />
);
DialogHeader.displayName = 'DialogHeader';

const DialogFooter = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2', className)} {...props} />
);
DialogFooter.displayName = 'DialogFooter';

const DialogTitle = ({ className, ...props }: ComponentProps<typeof DialogPrimitive.Title>) => (
  <DialogPrimitive.Title
    className={cn('text-display-l font-bold tracking-tight sm:text-xl sm:font-semibold', className)}
    {...props}
  />
);
DialogTitle.displayName = DialogPrimitive.Title.displayName;

const DialogDescription = ({ className, ...props }: ComponentProps<typeof DialogPrimitive.Description>) => (
  <DialogPrimitive.Description className={cn('text-sm text-neutral-500 dark:text-neutral-400', className)} {...props} />
);
DialogDescription.displayName = DialogPrimitive.Description.displayName;

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
};
