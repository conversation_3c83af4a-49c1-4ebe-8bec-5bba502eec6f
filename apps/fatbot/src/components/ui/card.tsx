import { cva, type VariantProps } from 'class-variance-authority';
import type { FunctionComponent, HTMLAttributes } from 'react';

import { cn } from '@/lib/utils';

const variants = cva('flex flex-col border-2 border-transparent', {
  variants: {
    variant: {
      'audit-info': 'bg-white/10',
      primary: 'rounded-md bg-surface-primary p-2',
      secondary: 'rounded-md border-border-subtle p-2',
      tertiary: 'rounded-md bg-surface-third-layer p-2',
      area: 'rounded-lg sm:p-3 bg-transparent sm:bg-surface-area',
      warning: 'rounded-md bg-event-warning-background p-2 text-event-warning-content',
      error: 'rounded-md bg-event-error-background text-event-error-content p-2',
      success: 'rounded-md bg-event-success-background text-event-success-content p-2',
      info: 'rounded-md bg-surface-brand-1-alpha-cards p-2 text-text-active',
      checking: '',
    },
  },
  defaultVariants: {
    variant: 'area',
  },
});

export type CardVariant = VariantProps<typeof variants>['variant'];

interface CardProps extends HTMLAttributes<HTMLDivElement>, VariantProps<typeof variants> {}

export const Card: FunctionComponent<CardProps> = ({ children, className, variant, ...rest }) => (
  <div className={cn(variants({ variant }), className)} {...rest}>
    {children}
  </div>
);
