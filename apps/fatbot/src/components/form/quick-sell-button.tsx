import { useTranslations } from 'next-intl';
import { match } from 'ts-pattern';

import { CloseX } from '@/assets';
import { CheckmarkIcon } from '@/assets/checkmark-icon';
import { SpinnerIcon } from '@/assets/spinner-icon';
import { toast } from '@/components/toast';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import { BotMarketPositionState, useForceSellMarketPositions } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Props {
  txState: BotMarketPositionState;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  botId: string | null;
  botTradeId: string;
}

const getTxStateIcon = (state: BotMarketPositionState): React.ReactNode =>
  match(state)
    .with(BotMarketPositionState.OPEN_FAILED, () => <CloseX className="size-2 text-event-error" />)
    .with(
      BotMarketPositionState.OPEN_NOT_LANDED,
      BotMarketPositionState.PENDING_CLOSED,
      BotMarketPositionState.PENDING_CLOSED_FROM_PROMOTED,
      BotMarketPositionState.PENDING_CLOSED_FROM_STALE,
      BotMarketPositionState.PENDING_CLOSED_FROM_RED_FLAG,
      BotMarketPositionState.PENDING_CLOSED_FROM_FORCE_SELL,
      () => <SpinnerIcon className="size-2 animate-spin" />,
    )
    .with(BotMarketPositionState.CONFIRMED_CLOSED, () => <CheckmarkIcon className="size-2" />)
    .otherwise(() => null);

export const QuickSellButton: React.FC<Props> = ({
  className,
  onClick,
  txState = BotMarketPositionState.OPENED,
  disabled,
  botId,
  botTradeId,
}) => {
  const t = useTranslations();

  const { mutate: forceSell, isPending } = useForceSellMarketPositions({
    mutation: {
      onSuccess: () => onClick?.(),
      onError: () => {
        toast.error(t('bot-trading.bot-trades.failed-to-sell-token'));
      },
    },
  });

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (disabled || !botId) {
      return;
    }
    forceSell({ botId, botMarketPositionId: botTradeId });
  };

  return txState === BotMarketPositionState.OPENED ? (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={cn('h-11 text-xs', {
            'cursor-not-allowed opacity-40': isPending || disabled,
            className,
          })}
          size="compact"
          variant="outline"
          onClick={handleClick}
        >
          {getTxStateIcon(txState)}
          {t('common.sell')}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{t('bot-trading.bot-trades.sell-token-now')}</TooltipContent>
    </Tooltip>
  ) : (
    <Button
      className={cn('h-11 text-xs', {
        'cursor-not-allowed opacity-40': isPending || disabled,
        className,
      })}
      size="compact"
      variant="outline"
      onClick={handleClick}
    >
      {getTxStateIcon(txState)}
      {t('common.sell')}
    </Button>
  );
};
