{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**/*", "tsconfig.build.tsbuildinfo"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"persistent": true, "cache": false, "outputs": [".next"]}, "clean": {"cache": false}, "format": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "../../prettier.config.js"], "outputs": []}, "format:fix": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "../../prettier.config.js"], "outputs": []}, "//#format:root": {"dependsOn": ["^build"], "outputs": []}, "//#format:root:fix": {"dependsOn": ["^build"], "outputs": []}, "lint": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "../../eslint.config.js"], "outputs": [".eslint<PERSON>che"]}, "lint:fix": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "../../eslint.config.js"], "outputs": [".<PERSON><PERSON><PERSON><PERSON>"]}, "//#lint:root": {"dependsOn": ["^build"], "outputs": [".eslint<PERSON>che"]}, "//#lint:root:fix": {"dependsOn": ["^build"], "outputs": [".eslint<PERSON>che"]}, "tc": {"dependsOn": ["^build"], "outputs": ["tsconfig.tsbuildinfo"]}, "test": {"dependsOn": ["^build"], "outputs": []}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**/*"]}, "ts": {"dependsOn": ["^build"], "outputs": ["tsconfig.tsbuildinfo"]}}}